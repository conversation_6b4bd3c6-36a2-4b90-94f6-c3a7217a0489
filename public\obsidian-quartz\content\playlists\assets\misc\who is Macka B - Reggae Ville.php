<?php
// Auto-generated blog post
// Source: content\playlists\assets\misc\who is Mack<PERSON> B - Reggae Ville.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Macka Bhttps://www.reggaeville.com/artist-details/macka-b/details/ "Macka B"  ADDhttps://www.reggaeville.com/artist-details/macka-b/about/ "ADD to M...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Macka Bhttps://www.reggaeville.com/artist-details/macka-b/details/ "Macka B"  ADDhttps://www.reggaeville.com/artist-details/macka-b/about/ "ADD to M...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\misc\\who is Macka B - Reggae Ville.md',
);

// Post content
$post_content = '<h1><a href="https://www.reggaeville.com/artist-details/macka-b/details/ "Macka B"" class="external-link">Macka B</a>  <a href="https://www.reggaeville.com/artist-details/macka-b/about/# "ADD to MYREGGAEVILLE"" class="external-link">ADD</a></h1>
<p><a href="https://www.reggaeville.com/artist-details/macka-b/news/ "News"" class="external-link">News</a> <a href="https://www.reggaeville.com/artist-details/macka-b/about/ "Bio"" class="external-link">Bio</a> <a href="https://www.reggaeville.com/artist-details/macka-b/photos/ "Photos"" class="external-link">Photos</a> <a href="https://www.reggaeville.com/artist-details/macka-b/videos/ "Videos"" class="external-link">Videos</a> <a href="https://www.reggaeville.com/artist-details/macka-b/releases/ "Releases"" class="external-link">Releases</a> <a href="https://www.reggaeville.com/artist-details/macka-b/music/ "Music"" class="external-link">Music</a> <a href="https://www.reggaeville.com/artist-details/macka-b/dates/ "Dates"" class="external-link">Dates</a></p>
<p>!<a href="https://www.reggaeville.com/fileadmin/user_upload/mackab.jpg" class="external-link">Macka B</a></p>
<p>Macka B has always been interested in music. At a very young age he was impressed by the heavyweight sound system Lord Barley, playing in the blues party next door. The vibrant reggae beat found a place in his heart that remains there to this very day.  
  
At school he played the violin and he joined the school choir. At home he was getting inspiration from Reggae music, listening to such greats as Burning Spear, Bob Marley, Peter Tosh, Count Ossie, to name but a few. On leaving school he sought a career in engineering. He obtained a technical apprenticeship at Ever Ready, but redundancy due to factory closure ended that career, which proved to be a blessing in disguise. He started to practice D.J.ing at home gaining inspiration from legends such as U-Roy, I-Roy, Big Youth, Prince Far-I, etc.  
  
It was also at this time that Macka B decided to accept the Ras Tafari faith, which is the central part of his life. He and some friends started a sound system called Exodus, which just happened to be the same sound that Macka B used to listen to as a little youth. The sound was passed down from father to sons and renamed Exodus.  
  
In 1982 a visit to Jamaica inspired him greatly improving his talent. He entered a D. J. competition in 1983 at the Rising Star club in Bilston, (included were Pato Banton & Rankin Ann.) MackaB. won and from this was invited on the radio several times, his name began to spread.  
  
He was also in a band called Pre-Wax, they did many live shows and also appeared on Channel 4’s Rockers Road show. A record called Maggie’s Letter was also released by a producer called Papa P. It became a local success. Derek Nelson producer of Ebony BBC2, heard Macka B perform on the radio, and as they had a show planned for Birmingham, asked him to perform on that show. He went down so well he received a regular spot on the next series. Macka B never stopped working with sound systems Wassifa from Birmingham & Skippy & Lippy being the main ones. A tape of a dance Wassifa v Saxon Sound in Leicester, was heard by John & Chris at Fashion records who invited Macka B to do some recording. “Bible Reader” was released and did well in the Reggae singles charts. Also released was a track called “Gentleman with Manners” on a LP called Great British M.C’s.  
  
Mad Professor of Ariwa saw Macka B perform on T.V. and arranged a meeting. The outcome of this was Macka B joining the Ariwa label and recording the LP “Sign of the Times” which reached No 1. in the Reggae LP charts. Appeared on Channel 4 Club Mix & 1986 Sunsplash in Wembley Arena. Started to tour Europe. Released 2nd album “We’ve Had Enough” in 1987. In 1988 visited Jamaica recorded “Love it in Jamaica” & “Slow Down Driver” for Black Scorpio label. Recorded “Looks are Deceiving” for Ariwa.  
  
Appeared on BBC 2’s Def 11 – Behind the Beat.  
  
Released LP Buppie Culture in 1989. The album contained the single “Dread a who she love” a duet with Kofi, which went to number one in the reggae singles charts.  
  
1990 saw the release of “Proud of Mandela” celebrating the release of Nelson Mandela. It went straight to number one in the Reggae singles chart and was featured on the album “Natural Suntan”.  
  
Became the first British Reggae artist to tour Australia.  
In the nineties Macka B recorded 8 more albums for Ariwa and also collaborated with other artists and labels. Albums for Ariwa were “Peace Cup”- “Discrimination” – “Roots Ragga” – “Jamaica No Problem” – “Here Comes Trouble” – “Hold On To your Culture” – “Suspicious” – “Roots & Culture”. Recorded “Tongue a no Gun” & “God a Come” with Sanchez on Xterminator label. “D. J. Unity” with Tony Rebel on Penthouse label. “Sex Machine” on Black Scorpio label. “Mr Cop” with Gregory Isaacs on Ras label. “Yele” with Baba Maal on Island records. “Doctors of love” with Pato Banton. “Malcolm X” with Steel Pulse.  
  
Recorded on Ariwa with Nolan Irie, Earl 16, Horace Andy, Sandra Cross, Kofi, Brown Sugar, Steve Ital, Fish.  
  
Voted British Reggae Industry Best Male D. J. 1991 and 1992.  
  
He has appeared on Channel 4’s “The Real McKoy” , Canal + in France, German TV and many stations worldwide.  
  
He has twice performed on Reggae Sunsplash in Jamaica, also at Sting & White River Reggae Bash in Jamaica. He also appeared in front of Nelson Mandela in the National Stadium Kingston. Macka B has toured Japan all over America, Africa, Hawaii, France, Germany, Belgium, Sweden, New Zealand, Australia, Italy, Spain, Poland to name but a few. He has performed alongside artists such as Burning Spear, U-Roy, The Wailers, Kool and the Gang, Lee Perry, Culture, Gladiators and many more.  
  
In the year 2000 he released the album “Global Messenger” on the Ariwa label. He also recorded “What’s going on” with Tony Rebel on the Flames label. He appeared on Rebel Salute in Jamaica and also toured South Africa.  
  
In 2001 he recorded 2 songs on the Humal label “Homeland Africa” & “More Knowledge” which were very well received. In 2002 Macka B teamed up with the Royale Roots Band and produced the album entitled “By Royale Command” which is still in demand.In 2003 Macka B played for the first time in West Africa performing in Senegal alongside Baba Maal and also in Sierra Leone.  
  
He also become the first Reggae artist to tour the former Yugoslavia (Slovenia, Croatia, Serbia).  
  
In 2004 Macka B signed to the Jet Star label (Charm) releasing the album “Word,Sound and Power “.In 2008 a brand new live Cd and also the first live Dvd featuring Macka B was released.2009 saw Macka.B’s Cd ”More Knowledge” released by Humal Records.  
    
2012 Recorded new album Change The World for Chinelo Records and EP Rasta Soldier for Necessary Mayhem, Raspect for El Ricallan (Japenese Label).  
  
2015 Macka B has Released “Never Played A 45” album on Peckings/VP which is being hailed as his best album to date. The title track was first released on 7inch 45 vinyl in 2012 and became an anthem.  
  
2016 Macka B embarked on his Never Played A 45 Asia Tour visiting countries like Philippines, Vietnam, Thailand, China, Hong Kong and Japan.  
  
After over 30 years Macka B continues to tour the world extensively spreading consciousness and breaking down the barriers.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>