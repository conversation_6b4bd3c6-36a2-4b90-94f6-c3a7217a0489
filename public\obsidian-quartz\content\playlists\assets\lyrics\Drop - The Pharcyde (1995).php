<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Drop - The Pharcyde (1995).md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'The Pharcyde - Drop 1995  !https://lh7-rt.googleusercontent.com/docsz/AD4nXdw1WJGyj-zmr08SoTXdZhqSI4ut9No-os9MteGNBg58ZMpuya2wzSKWjK5nXPp-UV3h4ekBe9fI...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'The Pharcyde - Drop 1995  !https://lh7-rt.googleusercontent.com/docsz/AD4nXdw1WJGyj-zmr08SoTXdZhqSI4ut9No-os9MteGNBg58ZMpuya2wzSKWjK5nXPp-UV3h4ekBe9fI...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Drop - The Pharcyde (1995).md',
);

// Post content
$post_content = '<p>**</p>
<p>The Pharcyde - Drop (1995)</p>
<h2>![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXdw1WJGyj-zmr08SoT_XdZhqSI4ut9No-os9MteGNBg58ZMpuya2wzSKWjK_5nXPp-UV3h4ekBe9fI9oWNU3sJHKIhjeCI8hTsmWoj0OuSQza8EJhVG9pN2tS7crFpyyqGqwnyIGdNL8Rnme-kGDFtUUog?key=wv1vqhlItAmyiVjj9PhYmg)</h2>
<h2>Music video<a href="https://en.wikipedia.org/w/index.php?title=Drop_(The_Pharcyde_song" class="external-link">[edit</a>&action=edit&section=1)]</h2>
<p>The music video for "Drop" was directed by <a href="https://en.wikipedia.org/wiki/Spike_Jonze" class="external-link">Spike Jonze</a> and filmed in <a href="https://en.wikipedia.org/wiki/Los_Angeles" class="external-link">Los Angeles</a>. The video features footage of the group performing the song backwards, replayed backwards, which when combined with the chopped, spacey beat of the song gives the video a slight surrealistic quality.[[1]](https://en.wikipedia.org/wiki/Drop_(The_Pharcyde_song)#cite_note-1) The group also worked with linguistic experts to recite the entire song backwards. <a href="https://en.wikipedia.org/wiki/Ad_Rock" class="external-link">Ad Rock</a> and <a href="https://en.wikipedia.org/wiki/Mike_D" class="external-link">Mike D</a> of the <a href="https://en.wikipedia.org/wiki/Beastie_Boys" class="external-link">Beastie Boys</a> make a brief cameo. The end of the video also features art from professional skateboarder/artist <a href="https://en.wikipedia.org/wiki/Mark_Gonzales" class="external-link">Mark Gonzales</a>.</p>
<p><a href="https://www.youtube.com/watch?v=wqVsfGQ_1SU" class="external-link">The Pharcyde - Drop (Official Music Video) - YouTube</a></p>
<p>![](https://lh7-rt.googleusercontent.com/docsz/AD_4nXcxHsaj9CfUL4kaxJ7ViJj4J3VKrXLTRUkypGV5DzM6fmOraECHHJITWTtjfXKSRyWm3l-Xm7MtT0hawrP844t8yVVDEvCMjboktXn_mZdGMtMBszNvjKhl7uTiJWw6sfdOmN8flBl0X0AmhRArLD6tEA?key=wv1vqhlItAmyiVjj9PhYmg)</p>
<p><a href="https://www.musicismysanctuary.com/the-making-of-the-pharcyde-drop-video-by-spike-jonze" class="external-link">The Making of The Pharcyde “Drop” Video by Spike Jonze | Music Is My Sanctuary</a></p>
<p>**</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>