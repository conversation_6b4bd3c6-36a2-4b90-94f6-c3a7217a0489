<?php
// Auto-generated blog post
// Source: content\ptsd-myth\trans-teens.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'trans teens are the strongest people alive - YouTubehttps://www.youtube.com/shorts/tLIR7aKN5Ss <iframe width="560" height="315" src="https://www.youtu...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'trans teens are the strongest people alive - YouTubehttps://www.youtube.com/shorts/tLIR7aKN5Ss <iframe width="560" height="315" src="https://www.youtu...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\trans-teens.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/shorts/tLIR7aKN5Ss" class="external-link">trans teens are the strongest people alive - YouTube</a></p>
<p><iframe width="560" height="315" src="https://www.youtube.com/embed/tLIR7aKN5Ss?si=gMYznJRmpxqLrl3w" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe></p>
<h1>trans teens are the strongest people alive</h1>
<p><a href="https://www.youtube.com/@LuxanderReal" class="external-link">Luxander</a></p>
<p>1,952 views Mar 5, 2024</p>
<p>I I can\'t imagine being a teenager today</p>
<p>I think it would be crushing I think I</p>
<p>would be deeply suicidal I have come</p>
<p>through a lot of that and I\'m on the</p>
<p>other side of it I\'ve been in my therapy</p>
<p>but if I didn\'t have a therapist I would</p>
<p>I would probably be very much on that</p>
<p>side of like I don\'t know if I can make</p>
<p>it in this world now I\'m angry and like</p>
<p>I\'ve been hardened by my experiences but</p>
<p>I also did not I grew up in a period of</p>
<p>time where people were mostly neutral</p>
<p>toward the idea of trans folks things</p>
<p>were starting to get a little bit better</p>
<p>with acceptance it\'s just it\'s a totally</p>
<p>different world now trans youth who are</p>
<p>out there surviving today are are truly</p>
<p>the strongest of us</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>