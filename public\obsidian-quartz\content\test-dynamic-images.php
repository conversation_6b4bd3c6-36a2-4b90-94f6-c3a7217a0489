<?php
$page_title = "Testing Dynamic Image References";
$meta_description = "Testing the new dynamic image reference system that pulls data from gallery.json";
$meta_keywords = "test, images";

$content = '---
title: Testing Dynamic Image References
author: <PERSON><PERSON> <PERSON><PERSON>
excerpt: Testing the new dynamic image reference system that pulls data from gallery.json
date: 2025-01-08
categories:
  - Test
tags:
  - test
  - images
---

# Testing Dynamic Image References

This post tests the new dynamic image reference system. Instead of manually writing `<img>` tags with paths and alt text, I can now just reference images from my gallery using simple syntax.

## Self Images

Here\'s me angling in a hammock:
<div class=\"dynamic-image-container\"><img src=\"img/self/30degrees.png\" alt=\"Angling in a hammock for optimal comfort.\" loading=\"lazy\" width=\"1028\" height=\"838\"><div class=\"image-caption\">Angling in a hammock for optimal comfort.</div><button class=\"toggle-commentary\" onclick=\"toggleCommentary(\'commentary-695c90cd6bc1f1aed85f5a5b79429166\')\">Show Commentary</button><div id=\"commentary-695c90cd6bc1f1aed85f5a5b79429166\" class=\"image-commentary\" style=\"display: none;\"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div>

And here\'s me proving I\'m not Mr. Smee:
<div class=\"dynamic-image-container\"><img src=\"img/self/not-smee.png\" alt=\"\" loading=\"lazy\" width=\"1204\" height=\"1599\"></div>

## Humor Images

A classic meme about social media validation:
<div class=\"dynamic-image-container\"><img src=\"img/humor/22-likes.png\" alt=\"meme text - I once posted a meme that got 22 likes so maybe you should think twice before you speak to me that way\" loading=\"lazy\" width=\"1080\" height=\"1105\"><div class=\"image-caption\">meme text - I once posted a meme that got 22 likes so maybe you should think twice before you speak to me that way</div><button class=\"toggle-commentary\" onclick=\"toggleCommentary(\'commentary-2bc6943f205e5ea1eee1893220f67f21\')\">Show Commentary</button><div id=\"commentary-2bc6943f205e5ea1eee1893220f67f21\" class=\"image-commentary\" style=\"display: none;\"><strong>Commentary:</strong> I don\'t go to Facebook anymore..</div></div>

## Art Images

Some thought-provoking art:
<div class=\"dynamic-image-container\"><img src=\"img/art/MiniCarMiniProfit.png\" alt=\"Image of three wheeled mini car with Henry Ford quote Mini cars drive a mini profit\" loading=\"lazy\" width=\"403\" height=\"292\"><div class=\"image-caption\">Image of three wheeled mini car with Henry Ford quote Mini cars drive a mini profit</div><button class=\"toggle-commentary\" onclick=\"toggleCommentary(\'commentary-17c305b9f84131409c9c12e2448c9957\')\">Show Commentary</button><div id=\"commentary-17c305b9f84131409c9c12e2448c9957\" class=\"image-commentary\" style=\"display: none;\"><strong>Commentary:</strong> We don\'t have better cars because better cars mean less money for car companies and industry as a whole.</div></div>

## Educational Content

Important information about trauma responses:
<span class=\'missing-image\'>[Image not found: traumamanifesto.jpg]</span>

## Testing Different Reference Styles

### By basename only
<div class=\"dynamic-image-container\"><img src=\"img/self/30degrees.png\" alt=\"Angling in a hammock for optimal comfort.\" loading=\"lazy\" width=\"1028\" height=\"838\"><div class=\"image-caption\">Angling in a hammock for optimal comfort.</div><button class=\"toggle-commentary\" onclick=\"toggleCommentary(\'commentary-5c54628d797b21698362e7a779213d11\')\">Show Commentary</button><div id=\"commentary-5c54628d797b21698362e7a779213d11\" class=\"image-commentary\" style=\"display: none;\"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div>

### By full path
<div class=\"dynamic-image-container\"><img src=\"img/self/30degrees.png\" alt=\"Angling in a hammock for optimal comfort.\" loading=\"lazy\" width=\"1028\" height=\"838\"><div class=\"image-caption\">Angling in a hammock for optimal comfort.</div><button class=\"toggle-commentary\" onclick=\"toggleCommentary(\'commentary-e996d5964098709c8edb65c87f1c8dcc\')\">Show Commentary</button><div id=\"commentary-e996d5964098709c8edb65c87f1c8dcc\" class=\"image-commentary\" style=\"display: none;\"><strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can\'t do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.</div></div>

### Non-existent image (should show error)
<span class=\'missing-image\'>[Image not found: this-does-not-exist.png]</span>

## Benefits of This System

1. **Single source of truth**: All image metadata lives in gallery.json
2. **Automatic alt text**: No more forgetting accessibility
3. **Rich captions**: Automatic captions from my curated descriptions
4. **Commentary**: Optional deeper context with toggle buttons
5. **Consistent styling**: All images follow the same design
6. **No broken paths**: No more `../../img/` path issues
7. **Easy maintenance**: Update alt text once, changes everywhere

This makes managing images across my blog so much easier!
';

include "page template.htm";
?>