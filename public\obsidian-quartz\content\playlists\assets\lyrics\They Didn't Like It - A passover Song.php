<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\They Didn't Like It - A passover Song.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '"They Didn\'t Like It": a Passover song by <PERSON> "They Didn\'t Like It": a Passover song by <PERSON>https://www.youtube.com/watch?v...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => '"They Didn\'t Like It": a Passover song by Daniel Maté "They Didn\'t Like It": a Passover song by Daniel Maté - YouTubehttps://www.youtube.com/watch?v...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\They Didn\'t Like It - A passover Song.md',
);

// Post content
$post_content = '<h1>"They Didn\'t Like It": a Passover song by Daniel Maté</h1>
<p><a href="https://www.youtube.com/watch?v=-zcfcEDv4Jk" class="external-link">"They Didn\'t Like It": a Passover song by Daniel Maté - YouTube</a></p>

<p><a href="https://yt3.ggpht.com/ytc/AIdro_nYYO28gj22dxNdgzY6D4bySoTnhOo_aGr5uqLsL54U-wC8=s48-c-k-c0x00ffffff-no-rj" class="external-link">![</a>](https://www.youtube.com/@danielmatemusic)</p>
<p><a href="https://www.youtube.com/watch?v=-zcfcEDv4Jk" class="external-link">Fetching Title#xihf</a>
<a href="https://www.youtube.com/@danielmatemusic" class="external-link">Daniel Maté</a></p>
<p>24.1K subscribers</p>
<p><__slot-el></p>
<p>Subscribe</p>
<p><__slot-el></p>
<p>154</p>
<p>Share</p>
<p>Download</p>
<p>Clip</p>
<p>Save</p>
<p>3,285 views Apr 21, 2011</p>
<p>World premiere! (pardon the stumbles & flubs) Live at the 2011 Maté-Mate seder, Vancouver BC. Lead vocals & guitar: Daniel Maté. Backing vocals & narrative fill-ins: assembled Matés, Mates, Asmouchas, and guests. For more, go to www.danielmate.com .</p>
<p>Transcript</p>
<p>Follow along using the transcript.</p>
<p>wasn\'t how did he find out that he</p>
<p>wasn\'t an Egyptian did she always know</p>
<p>did he always</p>
<p>know because his mother was brought to</p>
<p>nurse him okay but not</p>
<p>told the importance of attachment</p>
<p>parenting was known even there even by</p>
<p>the</p>
<p>Egyptians so Moses grew up safe and</p>
<p>sound and I guess life was pretty good</p>
<p>till one day he saw a guard beat a slave</p>
<p>and Moses killed him where he stood he</p>
<p>didn\'t do it to be macho that\'s not the</p>
<p>way he was no Moses was a simple man he</p>
<p>did it just because he didn\'t like it it</p>
<p>he didn\'t like it made him angry don\'t</p>
<p>you know he didn\'t like it he like it he</p>
<p>didn\'t like it didn\'t like it and then I</p>
<p>guess he figured yeah I should probably</p>
<p>go so where did he</p>
<p>go to desert right and he came what did</p>
<p>he become he became a</p>
<p>Shepherd well One Fine Day Moses was</p>
<p>hurting sheep or whatever it is</p>
<p>Shepherds do when he saw a bush all</p>
<p>covered in flame and the Bush was</p>
<p>talking</p>
<p>to the bush said Moses this is the Lord</p>
<p>your God now get off your your Shepherd</p>
<p>but go back to pharaoh and free the</p>
<p>slaves and Moses was like um what he</p>
<p>didn\'t like it didn\'t like it he didn\'t</p>
<p>like it like it he said Lord I\'m much</p>
<p>too shy he didn\'t like it like it he</p>
<p>didn\'t like it it for good luck trying</p>
<p>to argue with I don\'t</p>
<p>I he drives a hard bargain so did Moses</p>
<p>go</p>
<p>back okay and and uh he went to</p>
<p>Pharaoh\'s Palace so Moses said to</p>
<p>Pharaoh\'s face who sorry so Moses said</p>
<p>to Old Pharaoh whom he clearly was quite</p>
<p>afraid of he said Pharaoh you know this</p>
<p>is kind of awkward since you\'re kind of</p>
<p>like my</p>
<p>Zeta but there\'s a plague of paloa</p>
<p>coming your way if you don\'t free the</p>
<p>Hebrew</p>
<p>race and Pharaoh was quiet for a good</p>
<p>long while but you could tell just from</p>
<p>his face that he didn\'t like it it he</p>
<p>didn\'t like it he stand up and applaud</p>
<p>he didn\'t like it it he didn\'t like it</p>
<p>it he said you don\'t scare me you or</p>
<p>your</p>
<p>god well so the plagues came down one at</p>
<p>a time and things got pretty</p>
<p>strange frogs in hail and total darkness</p>
<p>well talk about climate</p>
<p>change and the Egyptians suffered</p>
<p>bitterly but Pharaoh stood his ground</p>
<p>and I know we\'re not supposed to see his</p>
<p>face but I\'ll bet elohenu frowned cuz he</p>
<p>didn\'t like it it he didn\'t like it</p>
<p>didn\'t like it Mom that\'s Turn the Page</p>
<p>Turn the Page here we go he didn\'t like</p>
<p>it it didn\'t like it it he said well</p>
<p>I\'ll show them he didn\'t like it it he</p>
<p>didn\'t like it it he said one more more</p>
<p>plague and they\'ll know who\'s</p>
<p>Hashem and what was that plague</p>
<p>folks May death of the firstborn death</p>
<p>of the first</p>
<p>born so Moses got word to the Israelites</p>
<p>and made sure they knew the score if you</p>
<p>want to keep your firstborn son paint</p>
<p>Lamb\'s blood on your door it was a</p>
<p>horrible night for Egyptians the angel</p>
<p>of death struck deep and a whole a lot</p>
<p>of mothers lost their voice and what</p>
<p>about all those sheep they didn\'t like</p>
<p>it didn\'t like it they didn\'t like it</p>
<p>like it being used as red door paint</p>
<p>they didn\'t like it it they didn\'t like</p>
<p>it it so much blood I think I\'m going to</p>
<p>faint okay I\'m</p>
<p>fine well Pharaoh\'s heart it softened up</p>
<p>or or else he\'d had enough cuz he said</p>
<p>okay the slaves can go but just leave me</p>
<p>all their stuff so the Hebrews left as</p>
<p>fast as they could a few belongings on</p>
<p>their backs no time to stop and bake</p>
<p>their bread and no time to relax guess</p>
<p>what they didn\'t like it it they didn\'t</p>
<p>like it no they complained with every</p>
<p>step they didn\'t like it it they didn\'t</p>
<p>like it like it even way back then we\'</p>
<p>never enjoyed a</p>
<p>slep</p>
<p>all right only three more verses folks</p>
<p>how you doing good all</p>
<p>right well Pharaoh woke up the very next</p>
<p>day and he felt a bit</p>
<p>depressed no one to fix him his bacon</p>
<p>and eggs and no one to wax his</p>
<p>chest you don\'t know what that is don\'t</p>
<p>worry well he thought about those Happy</p>
<p>Times his dear slave slaves had provided</p>
<p>and then he looked around at his slav</p>
<p>less world and you know what he decided</p>
<p>he didn\'t like it he it he didn\'t like</p>
<p>it it Go Jews to do his bidding he</p>
<p>didn\'t like it he like it he didn\'t like</p>
<p>it it so he announced guess what</p>
<p>kidding so just as the Hebrew runaways</p>
<p>reached the banks of the Great Red</p>
<p>Sea hungry and exhausted but still</p>
<p>pretty happy to be free they looked off</p>
<p>in distance and saw horses and armored</p>
<p>men there was no escape to either side</p>
<p>and the Jews said not again no they</p>
<p>didn\'t like it it they didn\'t like it it</p>
<p>as they face this new assault they</p>
<p>didn\'t like it it they didn\'t like it it</p>
<p>mes this you old fool this is all your</p>
<p>fault and so what happened</p>
<p>then asked God if he if he could help</p>
<p>them and he said Ro staff in the water</p>
<p>and did that it\'s part of the Red Sea</p>
<p>and then when so they pass through and</p>
<p>when all the je pass through um when the</p>
<p>Egyptians were about to go through it</p>
<p>the water closed on them the water</p>
<p>closed on them and the Jews escaped into</p>
<p>the desert right for 40 more years of</p>
<p>fun all right and we could we could sing</p>
<p>with that but this is where the story</p>
<p>ends so so why do you think we tell this</p>
<p>tale every year around this table is it</p>
<p>just a story of make believe some dusty</p>
<p>ancient</p>
<p>Fable well maybe it\'s to remind</p>
<p>ourselves that there are Egypts</p>
<p>everywhere and when we see people trying</p>
<p>to be free we can say hey we\'ve been</p>
<p>there and you know what we didn\'t like</p>
<p>it it we didn\'t like it like it so we</p>
<p>get it we understand we didn\'t like it</p>
<p>it we didn\'t like it and we\'re here to l</p>
<p>a hand one more time we didn\'t like it</p>
<p>didn\'t like it we didn\'t like it like it</p>
<p>so we get it we understand we didn\'t</p>
<p>like it</p>
<p>like it like it it cuz no one ought to</p>
<p>live in Egypt\'s</p>
<p>land unless they like</p>
<p>it a good singing guys</p>
<p>wow</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>