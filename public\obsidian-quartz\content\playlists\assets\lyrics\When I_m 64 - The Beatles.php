<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\When I_m 64 - The Beatles.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'C When I get older losing my hair,                  G Many years from now. G Will you still be sending me a Valentine.                     C Birthday ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'C When I get older losing my hair,                  G Many years from now. G Will you still be sending me a Valentine.                     C Birthday ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\When I_m 64 - The Beatles.md',
);

// Post content
$post_content = '<p>C
When I get older losing my hair,
                 G
Many years from now.
G
Will you still be sending me a Valentine.
                    C
Birthday greetings, bottle of wine.</p>
<p>C
If I\'d been out till quarter to three.
C7                   F
Would you lock the door.
F              Fm       C              A
Will you still need me, will you still feed me,
D        G7     C
When I\'m sixty-four.</p>
<p>Am              E
You\'ll be older too,
Am            D7
   And if you say the word,
F       G         C
I could stay with you</p>
<p>C
I could be handy, mending a fuse
                        G
When your lights have gone.
G7
You can knit a sweater by the fireside
                C
Sunday morning, go for a ride, 
 
[Verse 4]
 
C
Doing the garden, digging the weeds,
C7                 F
Who could ask for more.
F              Fm       C              A
Will you still need me, will you still feed me
D        G7    C
When I\'m sixty-four.
 
 
[Bridge 2]
 
Am
Every summer we can rent a cottage,
               G                      Am
In the Isle of Wight, if it\'s not too dear
Am                  E
We shall scrimp and save
Am               D7
   Grandchildren on your knee
F     G         C
Vera, Chuck and Dave
 
 
[Verse 5]
 
C
Send me a postcard, drop me a line,
                  G
Stating point of view.
G
Indicate precisely what you mean to say
                 C
Yours sincerely, Wasting Away. 
 
[Verse 6]
 
C
Give me your answer, fill in a form
C7           F
Mine for evermore
F              Fm       C              A
Will you still need me, will you still feed me,
D7       G7    C
When I\'m sixty-four?</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>