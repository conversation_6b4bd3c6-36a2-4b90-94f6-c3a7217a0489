<?php
// Auto-generated blog post
// Source: content\playlists\Playlists.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Birthday Playlist Favorite Mercy Songs + For Everyone Born + Circle Drawn in Mercy + Come Walk with Us + Let us be Known + Tenemos esperanza + What I ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Birthday Playlist Favorite Mercy Songs + For Everyone Born + Circle Drawn in Mercy + Come Walk with Us + Let us be Known + Tenemos esperanza + What I ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\Playlists.md',
);

// Post content
$post_content = '<p>Birthday Playlist</p>
<p>Favorite Mercy Songs
+ For Everyone Born
+ Circle Drawn in Mercy
+ Come Walk with Us
+ Let us be Known
+ Tenemos esperanza
+ What I see
+ Crowded Table
+ And we pray / what we need
+ Everlasting Arms</p>
<p>Rainbow Songs
+ Carsie Blanton - Loving is easy
+ <strong>It could have been me - Holly Near</strong>
+ Angel Walk
+ Blowing in the Wind
+ Trees of the field
+ Hymn of Promise - In the Bulb
+ Where have all the flowers gone?
+ Lo Yisa Goy
+ We don\'t go to God\'s House - Chumbawumba</p>
<p>Hot 90\'s Mix
+ Madonna - Like a Prayer
+ Ace of base - The Sign
+ Pharcyde - Drop
+ Tribe Called Quest
+ Roll to Me - del A Mitri
+ Don\'t get me wrong
+ Clash - Train in Vain
+ Queen - Under Pressure
+ Tracy Chapman - Fast Car</p>
<p>Rainy Day Songs
+ Passenger - Let her Go
+ Love like you
+ Night Changes - One Direction
+ Sixty Scented Charisma Blackbirds
+ Christina Perri 1000 Years
+ Fields of Gold - Ganda Boys
+ Just a Rainy Day -</p>
<p>Jewish Songs
+ Arvoles
+ Ocho Kandelikas
+ They didn\'t like it
+ Az Nisht Karn</p>
<p>Women of Folk Music</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>