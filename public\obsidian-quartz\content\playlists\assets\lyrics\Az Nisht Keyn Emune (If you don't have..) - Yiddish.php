<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Az Nisht Keyn Emune (If you don't have..) - Yiddish.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Az Nisht Karn If you dunt have ...  Az nisht kayn emine tsizamen mitn gelt. if you don\'t have faith along with money Vus-zhe arbetste of der velt? Wha...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Az Nisht Karn If you dunt have ...  Az nisht kayn emine tsizamen mitn gelt. if you don\'t have faith along with money Vus-zhe arbetste of der velt? Wha...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Az Nisht Keyn Emune (If you don\'t have..) - Yiddish.md',
);

// Post content
$post_content = '<h2>Az Nisht Karn (If you dunt have ... )</h2>
<p>Az nisht kayn emine tsizamen mitn gelt.
if you don\'t have faith along with money</p>
<p>Vus-zhe arbetste of der velt?
What are you working for in this world?</p>
<p>Az nisht kayn bine tsizamen mitn gelt.
If you don\'t have understanding along with money</p>
<p>Vus-zhe broukhste di gantse velt?
What use do you have for the worfd?</p>
<p>Az nisht kayn emes tsizamen mitn gelt,
If you don\'t have truth along with monsy</p>
<p>Vus-zhe gayste of der velt?
Why do you walk the earth?</p>
<p>Az nisht kayn das tsizamen mitn gelt,
If you don\'t have knowledge along with money</p>
<p>Vus-zhe darfste die gantse velt?
What do you need the world for?</p>
<p>Ay, lay, lay, lay .....</p>
<p>Ay, lay, lay, lay .....</p>
<p>If you don\'t have humility along with
Az nisht kayn anuve tsizamen mitn gelt,</p>
<p>What will you answer in the true world
Vus veste entfern of der emesdiker velt?</p>
<p>Az nisht kayn tseduke tsizamen mitn gelt, 
If you don\'t have charity along with</p>
<p>Vus far a tsil hoste of der velt?
What kind of goal do you have in the world</p>
<p>Az nisht kayn rakhmunes tsizamen mitn gelt
If you don\'t have compassion alo</p>
<p>Vus-zhe raste die gantse velt?
Why turn the world upside down</p>
<p>Az nisht kayn tmimes tsizamen mitn gelt, 
If you don\'t have simplicity alor</p>
<p>Vus iz dayn takhles of der velt?
What is your task in this world</p>
<p>Ay, lay, lay, lay.</p>
<p>...</p>
<p>Ay, lay, lay, lay .....</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>