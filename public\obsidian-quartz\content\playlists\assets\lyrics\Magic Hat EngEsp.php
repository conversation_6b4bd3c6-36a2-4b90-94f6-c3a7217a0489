<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Magic Hat EngEsp.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Deep inside my soul, I got this everlasting light,  That shines like a Sun, And radiates on everyone. and the more that i give  the more i got to give...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Deep inside my soul, I got this everlasting light,  That shines like a Sun, And radiates on everyone. and the more that i give  the more i got to give...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Magic Hat EngEsp.md',
);

// Post content
$post_content = '
<p>Deep inside my soul,
I got this everlasting light, 
That shines like a Sun,
And radiates on everyone.</p>
<p>and the more that i give 
the more i got to give
it\'s the way that i live
it\'s what i\'m living for
And the song that we share,
Is always everywhere.</p>
<p>Put your love in the magic hat..</p>
<p>Deep inside my soul,
I got this all abundant glow,
that\'s rising like the Moon</p>
<p>The more I receive,</p>
<p>Magic hat
Ooo.
El Sombrero es magico.</p>
<p>Put your love in the magic hat, 
share it with your family
Put some love in the magic hat,
do so lovingly..</p>
<p>En lo profundo alma,
Tengo esta luz eterna,
Que brilla como un sol,
Y irradia en sobre todo.</p>
<p>y cuanto más doy
cuanto más para dar
es la forma vivir,
por lo que vivir</p>
<p>Pon tu amor en el sombrero mágico.</p>
<p>En lo profundo alma,
Tengo brillo abundante,
que está saliendo 
como la luna</p>
<p>We take your loving money and we turn it into food</p>
<p>Tomamos tu dinero y convertimos en comida.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>