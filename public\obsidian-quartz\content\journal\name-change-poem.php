<?php
// Auto-generated blog post
// Source: content\journal\name-change-poem.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'February 15, 2018 Poem about legally changing my name    A life unseen, a name unknown, An old identity, forever sown. Forms and officials, a rising d...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'February 15, 2018 Poem about legally changing my name    A life unseen, a name unknown, An old identity, forever sown. Forms and officials, a rising d...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\journal\\name-change-poem.md',
);

// Post content
$post_content = '<p>February 15, 2018</p>
<p>**</p>
<p>Poem about legally changing my name</p>

<p>A life unseen, a name unknown,</p>
<p>An old identity, forever sown.</p>
<p>Forms and officials, a rising dread,</p>
<p>Preferring death to a life unled.</p>

<p>A ghost in limbo, a silent plea,</p>
<p>No school, no doctor, no liberty.</p>
<p>Work and marriage, a distant dream,</p>
<p>A sorrowful existence, it would seem.</p>

<p>Trapped in shadows, a whispered sigh,</p>
<p>A living phantom beneath the sky.</p>
<p>Oh, to break free, to truly be,</p>
<p>From this sorrowful, unseen decree.</p>
<p>**</p>
<p>**</p>
<p>When I came out as trans and female, I was homeless and living in a new state. I desperately needed to start over fresh. I had no criminal record, no witness protection, and a fine social standing. Every form, application, interaction, or piece of writing I had to do under my old identity filled me with a dread I wouldn’t wish on my enemy.</p>

<p>Changing my name and gender marker on primary legal documents was a life-or-death necessity. The mismatch comes up on everything: housing, employment, government aid, medical care—you name it. Often, they say it doesn\'t matter, but name and gender are the first things they see. And “lying” on certain forms carries the penalty of perjury. I didn’t have income or a lawyer, and the state I live in (NC) isn’t very friendly to transgender people, nor do they make the process easy. The whole process took one and a half years and put the rest of my life in limbo.</p>

<p>I received a helping hand many times. The biggest help came in the form of a micro-cash grant from Trans Lifeline. Their nonprofit is run by other trans people and provides a 24/7 hotline to call, as well as direct cash programs to individuals for legal and medical-related transition costs.</p>

<p>Now, I am comfortable in the identity I made for myself. I am an aspiring home chef with a product line. I coach clients in cooking for health and cater for events. Had you asked me five years ago where I’d see myself today, I would have said dead. I have a life worth living, and I owe a big part of that to Trans Lifeline.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>