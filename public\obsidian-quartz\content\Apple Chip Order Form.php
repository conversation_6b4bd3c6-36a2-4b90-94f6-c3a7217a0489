<?php
// Auto-generated blog post
// Source: content\Apple Chip Order Form.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Apple Chip Order Form Order Information - Number of bags:       - Bag size:  specify if you have different sizes, e.g. small, medium, large      - Des...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Apple Chip Order Form Order Information - Number of bags:       - Bag size:  specify if you have different sizes, e.g. small, medium, large      - Des...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\Apple Chip Order Form.md',
);

// Post content
$post_content = '<h2>Apple Chip Order Form</h2>
<p>Order Information</p>
<p>- Number of bags: _______________________________________
    
- Bag size: ( specify if you have different sizes, e.g. small, medium, large)
    
- Desired flavor(s): (if you have multiple flavors)</p>
<p>Delivery Options</p>
<p>- In-person pickup: Yes No
    
- Mailing: Yes No</p>
<p>- If mailing, please provide shipping address:</p>
<p>- Street Address: _______________________________________
    
- City: _______________________________________
    
- State: _______________________________________
    
- Zip: _______________________________________
    
- Phone number: _______________________________________</p>
<p>Contact Information</p>
<p>- Name: _______________________________________
    
- Email: _______________________________________
    
- Phone number: _______________________________________</p>
<p>Special Instructions</p>
<p>- Is there anything else you\'d like to let me know? (e.g. special handling, gift message, etc.): _______________________________________</p>
<p>Payment</p>
<p>- Payment will be made separately. Please see payment instructions on the website or follow the link to make a secure payment.</p>
<p>Note</p>
<p>- Please allow [insert time frame, e.g. 3-5 business days] for processing and shipping (if applicable).
    
- If you have any questions or concerns, please don\'t hesitate to reach out to me at [insert contact email or phone number].</p>
<p>You can print this out and include it with your fliers or email it to interested customers. They can fill it out and return it to you, and then make a separate payment using your preferred method (e.g. PayPal, Venmo, etc.).</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>