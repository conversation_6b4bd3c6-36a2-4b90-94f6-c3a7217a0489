<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\All coming back.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Its All Coming Back to Me Now There were times when it all seemed too much To deal with dismal forecasts of great global devastation. So I just kept m...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Its All Coming Back to Me Now There were times when it all seemed too much To deal with dismal forecasts of great global devastation. So I just kept m...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\All coming back.md',
);

// Post content
$post_content = '<p>Its All Coming Back to Me Now</p>
<p>There were times when it all seemed too much
To deal with dismal forecasts of great global devastation.
So I just kept myself quite out of touch
Not able to deal with the distressing information</p>
<p>I felt myself shut down inside the moment that I heard
About the future we’d be leaving to our kids.
While inside there was deep worry, but I pushed that all away
And continued going on doing as I did.</p>
<p>But when you go forth like this
And I see you like that
Well, I have to admit that it’s all coming back to me
When you call out like this
And I hear you like that
I just have to admit that it’s all coming back to me
It’s all coming back, yes, it’s all coming back to me now
All too often it feels so very overwhelming
Not knowing where to start, you know I find it so confusing
As so many act as if things are still alright
Yes we know what is at stake, and how much we will be losing
I felt as if there was nothing much that I could do
Resigned myself to this most distressing fate
Couldn’t get myself to act, didn’t know which way to go
Yet kept praying that we wouldn’t start too late
So when our eyes meet like this
And you smile like that
Well, I have to admit that it’s all coming back to me
When our hand touch like this
And we circle like that
I am pleased to admit that it’s all coming back to me
It’s all coming back, well it’s all coming back to me now
What the future  may be
There is no guarantee
As we continue to strive
It is all coming back to me
When you march forth like this
And I join in like that
There’s a strength and a joy
Which is all coming back to me
It’s all coming back, yes, it’s all coming back to me
I am proud to admit that it’s all coming back to me now….</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>