<?php
// Auto-generated blog post
// Source: content\people helping people.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'I\'ve always believed in the power of people coming together to uplift each other. Throughout my life, I\'ve strived to embody the "People Helping Peopl...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'I\'ve always believed in the power of people coming together to uplift each other. Throughout my life, I\'ve strived to embody the "People Helping Peopl...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\people helping people.md',
);

// Post content
$post_content = '<p>I\'ve always believed in the power of people coming together to uplift each other. Throughout my life, I\'ve strived to embody the "People Helping People" philosophy and demonstrate leadership, excellence, and community involvement.</p>
<p>After receiving my Bachelor\'s degree in Psychology from Ohio University in 2015, I took a bold step and walked across America for environmental justice. Though I couldn\'t predict my future then, I knew I wanted to make a difference. Life threw challenges my way, such as homelessness and chronic unemployment, but these experiences only strengthened my resolve.</p>
<p>In 2018, I joined Green Opportunities\' vocational training program and became a Culinary Student. There, I founded April\'s Apple Chips, a business that uses donated apples to create delicious, dried fruit snacks. By offering free samples and partnering with food recovery organizations, I promote local food security in Appalachia and reduce waste.</p>
<p>At A-B Tech, I worked as a HelpDesk Associate, assisting students with their technological needs. Being bilingual in English and Spanish allowed me to bridge language gaps and foster inclusion among diverse student populations. While the HelpDesk position was unfortunately defunded, I remain dedicated to serving higher education and look forward to new opportunities.</p>
<p>Through my Digital Media coursework, I built a blog, <a href="http://www.aachips.co/" class="external-link">www.aachips.co</a>, centered around my business and personal brand. The blog shares \'Cool Facts,\' provides insightful content, and promotes the idea of \'Chips\' as a symbol of effective communication online. I aspire to use my writing skills to benefit a wider audience and plan to create an assessment tool for adults on the autism spectrum as part of my Web Developer certificate project.</p>
<p>Volunteer work is an integral part of my life. Working alongside food recovery organizations, I secure surplus produce to turn into apple chips and distribute them freely. Creating a climate-neutral snack production process aligns with my values and allows me to contribute positively to my community.</p>
<p>The "People Helping People" philosophy drives me to make a difference in the lives of others. Whether it\'s through my tech-centric projects, socially responsible business practices, or volunteer efforts, I continue to grow and inspire those around me. With one year left before obtaining my Web Developer certifications, I eagerly anticipate expanding my impact and deepening my connection to my community.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>