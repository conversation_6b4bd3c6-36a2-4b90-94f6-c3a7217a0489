<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Old 33's Songs.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Somebody Else, Not Me - <PERSON>ee <PERSON><PERSON> Twelfth Street Rag - Pee We<PERSON> Hunt He Sends Me - <PERSON><PERSON>er Come and Get it Honey - <PERSON><PERSON> Say a littl...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Somebody Else, Not Me - Pee Wee Hunt Twelfth Street Rag - Pee Wee Hunt He Sends Me - Nellie Lutcher Come and Get it Honey - Nellie Lutcher Say a littl...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Old 33\'s Songs.md',
);

// Post content
$post_content = '<p>Somebody Else, Not Me - Pee Wee Hunt
Twelfth Street Rag - Pee Wee Hunt
He Sends Me - Nellie Lutcher
Come and Get it Honey - Nellie Lutcher
Say a little prayer for me - Nellie Lutcher
My New Papa\'s Got to Have Everything - Nellie Lutcher
I know that you know - Yo Sé que tú sabes - Lionel Hampton and his Orchestra
On the Sunny Side of the Street - En la resonane de la caleya - Lionel Hampton and his Orchestra
John\'s Idea - Count Basie
One O\'Clock Jump - Count Basie
Stumpy - Coleman Hawkins
How Deep is the Ocean - Coleman Hawkins
Don\'t Start No Stuff - Harlem Hamfats
Let me Feel it - Harlem Hamfats
Moonlight Serenade - Glenn Miller and his Orchestra
Little Brown Jug - Glenn Miller and his Orchestra</p>
<p>Stompin\' at the Savoy - Benny Goodman Quartet
Vibraphone Blues - Benny Goodman Quartet
Can\'t Teach my Old heart New Tricks - Benny Goodman and his Orchestra
Silouetted in the Moonlight - Benny Goodman and his Orchestra
Liza - Benny Goodman Quartet
Smiles - benny Goodman Quartet
Avalon - benny Goodman Quartet
The Man I love - Benny Goodman Quartet
Oh Lady Be Good - Benny Goodman Trio
China Boy - Benny Goodman Trio
Rosetta - Benny Goodman and his Orchestra
I want to be happy - Benny Goodman and his Orchestra
Saving Myself for You - Benny Goodman and his Orchestra
Don\'t Wake Up my Heart - Benny Goodman and his Orchestra
You\'re My Ideal - Lionel Hampton and his Orchestra
The Sun will Shine Tonight - Lionel Hampton and his Orchestra
Scatter Brain - Benny Goodman and his orchestra
One Sweet Letter from you - Benny Goodman
You Brought a New Kind of Love to me - Benny Goodman
Close as Pages in a Book - Benny Goodman
If Dreams Come True - Benny Goodman
Life Goes to a Party - Benny Goodman</p>
<p>I get the Blues when it rains - Claude thornhill
There\'s a small hotel - Claude Thornhill
The Cafe Mozart Waltz - Anton Karas (from production The Third Man)
Once in a While - Tommy Dorsey
If it\'s the Last Thing I do - Tommy Dorsey
Blue Skys - Dinah Shore & Russ Case
How Deep is the Ocean - Dinah Shore  & Russ Case
Don\'t you love me anymore - Freddy Martin
When I write my Song - Freddy Martin
Nature Boy - King Cole
Lost April - King Cole Trio
Snowfall - Claude Thornhill
Autumn Nocturne - Claude Thornhill
Sometimes I feel like a motherless child - Sarah Vaughn
The Lord\'s Prayer - Sarah Vaughn
No Good Man - Billie Holiday
Good Morning Heartache - Billie Holiday
Fantasie - Chopin
Waltz in D Flat - Jose Iturbi
We Three ( My Echo, Shadow, and Me) - Ink Spots
Maybe - Ink Spots
If I didn\'t care - Jack Lawrence
Whispering Grass - Don\'t tell the trees - Ink Spots
I\'ll never smile again - Ink Spots
It will have to do Until the Real Thing Comes Along - Ink Spots
The Sweetest Dream - Ink Spots
I\'m going to turn off the tear drops - Ink Spots</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>