<?php
// Auto-generated blog post
// Source: content\climate\walkabout-weblog.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'From Walkabout to Weblog: Reclaiming My Voice for Climate Action This March of 2024 will mark the ten year anniversary of beginning my walk across Ame...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'From Walkabout to Weblog: Reclaiming My Voice for Climate Action This March of 2024 will mark the ten year anniversary of beginning my walk across Ame...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\climate\\walkabout-weblog.md',
);

// Post content
$post_content = '<p><strong>From Walkabout to Weblog: Reclaiming My Voice for Climate Action</strong></p>
<p>This March of 2024 will mark the ten year anniversary of beginning my walk across America – not for selfies or fame, but for a future worth breathing. As part of the Great March for Climate Action, I trekked through deserts, plains, and mountains, alongside a band of dedicated souls, fighting for the planet we call home. It was the best and worst time of my life, forging bonds and facing hardships as we brought our message to every town and city along the way.</p>
<p>But the walk, for all its transformative power, became my singular story. People remembered me as "the one who walked," not the person who yearned for a better world. For a while, I retreated, rebuilding my life from the ground up, seeking anonymity under a new name. Yet, the fire within never truly died.</p>
<p>Today, I\'m reclaiming my voice. Not to relive the past, but to share the lessons learned, the stories heard, and the unwavering hope that still burns bright. This blog is my bridge back to the world, a space to reconnect with those who walked alongside me, and perhaps, to reach others who haven\'t yet found their path in the fight for climate justice.</p>
<p>I don\'t expect to change the world overnight. I know our political landscape is fraught, and populist movements often fall on deaf ears. But every voice matters. Every story shared ignites another. Through my words, I aim to offer a glimpse into the heart of the climate movement, to shed light on the human cost of inaction, and to inspire even the smallest act of change.</p>
<p>This isn\'t about me anymore. It\'s about the shared responsibility we hold for this planet and its future. It\'s about the young faces I met along the way, eyes filled with both fear and hope. It\'s about remembering why we walked, why we spoke, and why we never gave up.</p>
<p>So, join me on this journey. Let\'s walk together, not across miles of asphalt, but through the labyrinthine corridors of change. Let\'s share our stories, listen to each other, and find the strength to rise beyond cynicism and apathy. We may not always know the destination, but the path we pave together leads towards a brighter tomorrow.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>