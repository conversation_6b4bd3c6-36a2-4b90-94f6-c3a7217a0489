<?php
// Auto-generated blog post
// Source: content\judaism\karl-gorath.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'This is <PERSON>. When he was 26, his jealous lover denounced him to the police as a gay man. The Nazi regime had strengthened Germany’s existing...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'This is Karl Gorath. When he was 26, his jealous lover denounced him to the police as a gay man. The Nazi regime had strengthened Germany’s existing...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\karl-gorath.md',
);

// Post content
$post_content = '<p>This is Karl Gorath. When he was 26, his jealous lover denounced him to the police as a gay man. The Nazi regime had strengthened Germany’s existing law against homosexuality, Paragraph 175, to punish any and all men suspected of so-called "unnatural indecency" with other men. After being denounced, Karl was tried and convicted under Paragraph 175 and sent to Neuengamme concentration camp, where he was forced to wear a pink triangle on his prisoner uniform.</p>
<p>His training as a nurse eventually got him transferred to another camp to work in the hospital there; however, when a guard ordered him to decrease the rations for patients who were Polish prisoners of war, Karl refused. He was sent to Auschwitz as punishment, where he remained until liberation in 1945.</p>
<p>After the war, he returned to Germany. His conviction under Paragraph 175 stayed on his record because West Germany continued to uphold the Nazi version of the law. Karl was arrested under Paragraph 175 a second time in the 1950s. At his trial, the judge sentencing him to prison for homosexuality was the same judge who had sent him to a concentration camp for the same reason decades earlier. From 1945 until Paragraph 175 was amended in 1969, West Germany imprisoned about 100,000 men under this law. The German government did not fully repeal Paragraph 175 until 1994.</p>
<p>Karl made several attempts to claim reparations after the war, as many survivors of Nazi persecution did. He was denied. According to officials, he was not eligible because his camp records showed that he was a Paragraph 175 prisoner. He became one of the first gay survivors of Nazi persecution to publicly tell his story. He died in 2003, after many years of sharing his experiences and drawing attention to the plight of Paragraph 175 prisoners. <a href="https://www.facebook.com/hashtag/neverforget?__eep__=6&__cft__[0]=AZV3swQPu67fbxIVJsdgOiBpydKd4D8WBbJec-0BnA4nSCeyIB9n7KmEDCwR7arSDX07oQKoHFa3BujYsrMG3nR7VGuYi_6HffqD7FgJ3C9874xx6LCm4LmkRxC_agTseu8&__tn__=*NK-R" class="external-link">#NeverForget</a></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>