[devinprater](https://forum.obsidian.md/u/devinprater)
[Jun '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669 "Post date")

Hi all. I’m a totally blind person that’s interested in using Obsidian in my job, and maybe personally as well. My boss told me about Obsidian–that’s what he uses–so I decided to try it out. I found that some of it was usable with a screen reader (I tried [NVDA on Windows](https://www.nvaccess.org/). I found that when I navigated text that I’d written, using the arrow keys, all I heard was “blank” signifying that the screen reader could not read the text.

### Steps to reproduce

Assuming NVDA is installed(get from link above):

-   Start NVDA.
-   Start Obsidian.  
    Close all open files, then create a new one. You can use arrow keys and Enter for this.
-   A file name box opens, and gains keyboard focus. The name of the field isn’t spoken with NVDA (much more minor bug). Enter any name, and press Enter.
-   Now, type some text. A heading, and then a paragraph, will do.
-   press the Up arrow a few times to get back to the heading.

### Expected result

After pressing the arrow keys, NVDA should be speaking text behind the cursor, or the line that the cursor is on.

### Actual result

NVDA speaks “blank” instead, meaning that NVDA sees no text at all. Interestingly, if I select all with Control + A, NVDA reads the text then. However, this is hardly a good editing experience.

### Environment

-   Operating system: Windows 10

-   Obsidian version: v0.12.4
# [Accessibility: Obsidian with screen readers](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669)

[Feature requests](https://forum.obsidian.md/c/feature-requests/8)

[editor-legacy](https://forum.obsidian.md/tag/editor-legacy)[ui-ux](https://forum.obsidian.md/tag/ui-ux)[valuable](https://forum.obsidian.md/tag/valuable)[upstream-bug](https://forum.obsidian.md/tag/upstream-bug)

[Jun 2021](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Jun 2021")

13 / 13

Mar 18

[Mar 19](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Mar 19")

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/d/67e7ee/90.png)](https://forum.obsidian.md/u/devinprater)

[devinprater](https://forum.obsidian.md/u/devinprater)

[Jun '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/4 "Post date")

I just tried both Narrator on Windows and VoiceOver on the Mac. Narrator works even more poorly than NVDA, and while I can review the lines I’ve written with VoiceOver, I cannot hear what my cursor passes over. I’ll try JAWS next. I’m an accessibility tester as part of my job, so I have access to just about any screen reader you’d like me to test with.

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/d/67e7ee/90.png)](https://forum.obsidian.md/u/devinprater)

[devinprater](https://forum.obsidian.md/u/devinprater)

[Jun '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/5 "Post date")

Okay, tried with JAWS and Orca (for Linux). JAWS did the same thing as VoiceOver, allowing me to review the text outside of the edit field, but I still cannot move my cursor there. The program didn’t work at all with Orca.

[![](https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png)](https://forum.obsidian.md/u/WhiteNoise)

[WhiteNoise](https://forum.obsidian.md/u/WhiteNoise)

[Jun '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/6 "Post date")

Thank you very much for your testing. Your efforts prompted me to do some research. The issues you are highlighting are due to our editor library Codemirror 5.  
Codemirror 5 doesn’t play well with screen reader due to some architectural decisions and technical limitations and it won’t get “fixed”.

[github.com/codemirror/CodeMirror](https://github.com/codemirror/CodeMirror/issues/4604)

#### [Accessibility for blind and visually impaired screen reader users 9](https://github.com/codemirror/CodeMirror/issues/4604)

opened  Feb 24, 2017

closed  Feb 25, 2017

 [![mjanusauskas](https://forum.obsidian.md/uploads/default/original/3X/2/5/250c21847cb9887bab230cefd8bd181f429d88ce.png) mjanusauskas](https://github.com/mjanusauskas)

The known issue of lack of compatibility with screen readers and assistive technology completely prevents blind and visually impaired screen reader...

Codemirror 6 is a complete rewrite of the editor and it does work with screenreaders. We already use Codemirror 6 in the mobile app and will eventually upgrade to Codemirror 6 in the desktop app as well.

3

3 MONTHS LATER

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/b/f4b2a3/90.png)](https://forum.obsidian.md/u/brocktaylor87)

[brocktaylor87](https://forum.obsidian.md/u/brocktaylor87)

[Sep '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/7 "Post date")

This is great, I’m very much looking forward to the transition to CodeMirror 6 on desktop. Is there any rough timeline on when that transition might happen? I know of a few visually impaired users that are anxious to try out Obsidian (and I’ve told them all how great it is), so I’m excited about this update! ![:smiley:](https://forum.obsidian.md/images/emoji/apple/smiley.png?v=10 ":smiley:")

1

[![](https://forum.obsidian.md/user_avatar/forum.obsidian.md/jbstep/90/1222_2.png)](https://forum.obsidian.md/u/jbstep)

[jbstep](https://forum.obsidian.md/u/jbstep)INSIDER

[Sep '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/8 "Post date")

I’m interested too. I’d like to introduce it to my students who are visually impaired. It’s a great tool, but until accessiblity issues are addressed, it’s a no go.

1

2 MONTHS LATER

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/v/278dde/90.png)](https://forum.obsidian.md/u/vitallie)

[vitallie](https://forum.obsidian.md/u/vitallie)

[Nov '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/9 "Post date")

I am on obsidian 0.12 as a blind user of the nvda screenreader.  
I would love to get in to obsidian, but am still not able to read text back while editing. Will this be fixed? How can we adres such a thing on the roadmap I mean accessibility should be a priority hthese days of all kinds of organisations. HOw can we get this properly adresed on the backlog? And fixed within one of the upcoming sprints?

[![](https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png)](https://forum.obsidian.md/u/WhiteNoise)

[WhiteNoise](https://forum.obsidian.md/u/WhiteNoise)

[Nov '21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/10 "Post date")

I am not 100% sure how things are now. 0.13 available to insiders now has the new editor. Let us know if it works better when you get chance.

2 MONTHS LATER

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/v/278dde/90.png)](https://forum.obsidian.md/u/vitallie)

[vitallie](https://forum.obsidian.md/u/vitallie)

[Jan 20](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/11 "Post date")

Seems wto work better, thnx.  
I enabled livepreview and now while my screenreader is in edit mode, I can read the text backwarts while editing in the same formfield.

[![](https://forum.obsidian.md/user_avatar/forum.obsidian.md/whitenoise/90/11_2.png)](https://forum.obsidian.md/u/WhiteNoise)

[WhiteNoise](https://forum.obsidian.md/u/WhiteNoise)

[Jan 21](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/12 "Post date")

[@devinprater](https://forum.obsidian.md/u/devinprater) Did you get the chance to test how the new editor in 0.13+ works with screen readers?

2 MONTHS LATER

[![](https://forum.obsidian.md/letter_avatar_proxy/v4/letter/b/3ab097/90.png)](https://forum.obsidian.md/u/bramd)

[bramd](https://forum.obsidian.md/u/bramd)

[Mar 19](https://forum.obsidian.md/t/accessibility-obsidian-with-screen-readers/19669/13 "Post date")

I just did a quick test in version 0.13.33:

-   In source view, the current editor works quite well
-   In preview view, the editor has serious issues in NVDA when links/embedded elements are in the text. NVDA reads the text before the link and stops. The only way to read further is moving through the text until the cursor is on the link
-   The rest of the interface is not really accessible. For example, I can use ctrl+p to open the command palette, but while typing and arrowing through the list I can’t read which command is selected. Because many functions are available from this palette and I don’t think it would be so hard to make this accessible, this might be a good first step. Another app based on web technologies which implements such a command palette really well is Visual Studio Code, you might get some inspiration there.
-   Other parts of the UI such as the explorer are not accessible from the keyboard. Ensure everything that should be focusable (such as buttons/links/input controls) are reachable by tab or shift+tab. Of course, tab has another function in the editor, so you need a way to get out of the editor first. A few possible approaches:
    -   Introduce a hotkey to switch tab between inserting a tab in the editor or moving focus. Visual Studio Code does this and assigns ctrl+m by default
    -   Introduce a hotkey to move between UI panes, f6 is a common choice in Windows applications. This could move between the most important parts of the UI and get you out of the editor without using tab.

Hope this helps.