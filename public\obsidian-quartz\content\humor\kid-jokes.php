<?php
// Auto-generated blog post
// Source: content\humor\kid-jokes.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '<PERSON> Jo<PERSON>! Why did the cloud wear his underwear? Because he wanted to wear his favorite ones: Thunderwear! Why did the cat meow at the dog? Because it...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Kid Jokes! Why did the cloud wear his underwear? Because he wanted to wear his favorite ones: Thunderwear! Why did the cat meow at the dog? Because it...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\humor\\kid-jokes.md',
);

// Post content
$post_content = '<p><strong>Kid Jokes!</strong></p>
<p>Why did the cloud wear his underwear? Because he wanted to wear his favorite ones: Thunderwear!</p>
<p>Why did the cat meow at the dog? Because it was a CATastrophic situation.</p>
<p>Why was the dog chasing the cat? Because it was a dog and catastrophic situation.</p>
<p>Why did the cat go to the farmer\'s farm? It was CAT Town there!</p>
<p>Why did the weiner dog roll on the ground? There was dog mud!</p>
<p>Why did the dandelion only grow when people threw dirt on it? It was a dirt flower.</p>
<p>Why did the house first explode when the people first bought it? It was a vol[cano] house!</p>
<p>Why did the vulture cross the road as fast as he could? He didn\'t want to get run over by a scrambled egg car!</p>
<p>Why did the girl living down the lane read so many books? Because she wanted to time travel across the world with a bunch of books like book time!</p>
<p>Why did the leather coat rip apart? Because there was too much underleather.</p>
<p>Why was the pop tart so sad? It got depressed in the toaster!</p>
<p>What kind of toucan bird has cheese for a beak? A TouCheese!</p>
<p>\'The one thing I don\'t like about love is how the girl and the boy get married, and you have to kiss the boy. Gross!\' \'What do you think of boys marrying boys or girls marrying girls?\' \'iunno\'</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>