<?php
// Auto-generated blog post
// Source: content\inspiration\help-during-panic-attack.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Ways to be helpful during a loved one\'s panic attack';
$meta_description = 'Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack. If possible, assist me to a safe spot. Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Ways to be helpful during a loved one\'s panic attack',
  'author' => 'Mental Health on the Mighty',
  'date' => '2025-08-05',
  'excerpt' => 'Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack. If possible, assist me to a safe spot. Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\help-during-panic-attack.md',
);

// Post content
$post_content = '<h3>Ways to be helpful during a loved one\'s panic attack</h3>
<p>- Talk to me to help keep me grounded. Once I\'m lost in my thoughts it\'s harder to get out of the panic attack.</p>
<p>- If possible, assist me to a safe spot.</p>
<p>- Don\'t undermine my panic. Don\'t yell at me. Acknowledge it. Understand. Have empathy.</p>
<p>- Don\'t ignore me because it\'s uncomfortable – remind me I\'m safe and that I\'m going to be ok.</p>
<p>- Don\'t bring more attention to me! The more people who see the panic in my eyes, the more I panic. I just need space, room to breathe and understanding.</p>
<p>- Distract me, talk about beautiful things.</p>
<p>- Help me focus elsewhere. Show me things aren\'t deadly or threatening. Distract me without judgment.</p>
<p>- Ask me if there\'s anything I need or anything you can do to help. If I say no, tell me you\'re there, I need you and that everything is going to be ok.</p>
<p>- Stay by my side, don\'t make it a big deal. Breathe with me.</p>
<p>- During a panic attack, ask if it\'s ok if you come close. Getting in my face can make the attack worse. Sometimes holding my hand helps, sometimes it\'s a trigger.</p>
<p>- Never tell the person having the attack to \'get over it.\'</p>
<p>- Remind me this won\'t last forever.</p>
<p>- Keep yourself calm. I will eventually feed off your calmness and I\'ll be able to calm down.</p>
<p>- Sometimes I don\'t need someone to try and fix it. Sometimes they just need to be there with a simple hug or to hold a hand.</p>
<p>From Mental Health on the Mighty</p>
<p><img src="../../img/gazarubble.webp" width="400" alt="rubble of destruction in gaza strip."></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>