<?php
// Auto-generated blog post
// Source: content\inspiration\paradox-of-age.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'The Paradox of our Age';
$meta_description = 'We have bigger houses, and smaller families. More convenience, but less time..';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'The Paradox of our Age',
  'author' => 'Dalai Lama',
  'date' => '2025-08-05',
  'excerpt' => 'We have bigger houses, and smaller families. More convenience, but less time..',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\paradox-of-age.md',
);

// Post content
$post_content = '<p><blockquote>We have bigger houses,<br>
and smaller families;<br>
More convenience,<br>
but less time;<br><br></p>
<p>we have more degrees, but less sense,<br>
more knowledge, but less judgment;<br>
more experts, but more problems;<br>
more medicine, but less health;<br><br></p>
<p>We have been all the way to the moon and back,<br>
but have trouble crossing the road to meet the new neighbor.<br></p>
<p>We built more computers to hold more information to produce more copies than ever,<br>
but less communication.<br><br></p>
<p>We have become long on quantity, but short on quality,<br>
These are times of fast foods,<br>
and slow digestion;<br><br></p>
<p>Tall man, and short character<br>
Steep profits, and shallow relationships,<br><br></p>
<p>It is time where there is much in the window,<br>
and nothing in the room.</blockquote></p>
<p>-HH Dalai Lama</p>
<p><img src="../../img/art/helpbysafaaodah.jpg" width="400" alt="Art help by Safaa Odah person consumed by barcode with teeth.">
Help by Safaa Odah</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>