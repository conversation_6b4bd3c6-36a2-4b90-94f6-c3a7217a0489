<?php
// Auto-generated blog post
// Source: content\inspiration\i-dont-help-my-wife.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'I don\'t help my wife clean the house..';
$meta_description = 'I sat back down with him again and explained to him that I don\'t ′′help′′ my wife. Actually, my wife doesn\'t need help, she needs a partner, a teammate. I\'m her home partner… and due to that, all functions are divided, which is not “help” with household chores.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'I don\'t help my wife clean the house..',
  'author' => 'Mum Central',
  'date' => '2025-08-05',
  'excerpt' => 'I sat back down with him again and explained to him that I don\'t ′′help′′ my wife. Actually, my wife doesn\'t need help, she needs a partner, a teammate. I\'m her home partner… and due to that, all functions are divided, which is not “help” with household chores.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\inspiration\\i-dont-help-my-wife.md',
);

// Post content
$post_content = '<h3><a href="https://www.facebook.com/MumCentralAU/?__cft__[0]=AZVdF4-G1dbnpODHpldExdynNVGw0V7eh131nDo5xc_BM0CtpqBDaJNzzL-eIqGfrZPOz3_AiSdXyPd726vDZa9qMZtdamjJilO9cf_VVltCIgbRmEAwB9GcDFcaEYY4iFrSy22CB530F5merRW7yQKL7TEU7K48wQOISAuDPvzgwu27uziZDBT9kTiS0raLpKn9yrxFG9x6XrJlI5jB1KGX&__tn__=-UC%2CP-y-R" class="external-link"><strong>Mum Central</strong></a> </h3>
<p><a href="https://www.facebook.com/?__cft__[0]=AZVdF4-G1dbnpODHpldExdynNVGw0V7eh131nDo5xc_BM0CtpqBDaJNzzL-eIqGfrZPOz3_AiSdXyPd726vDZa9qMZtdamjJilO9cf_VVltCIgbRmEAwB9GcDFcaEYY4iFrSy22CB530F5merRW7yQKL7TEU7K48wQOISAuDPvzgwu27uziZDBT9kTiS0raLpKn9yrxFG9x6XrJlI5jB1KGX&__tn__=%2CO%2CP-y-R#?ggb" class="external-link"><strong><em>*A</strong><strong>u</strong><strong>g</strong><strong>u</strong><strong>s</strong><strong>t</strong> <strong>3</strong> <strong>a</strong><strong>t</strong> <strong>4</strong><strong>:</strong><strong>0</strong><strong>0</strong> <strong>A</strong><strong>M</strong></em>*</a>  · </p>
<p><img src="../../img/edutainment/wifeSkills.jpg" width="400" alt="Housework is a life skill, not a wife skill."></p>
<p>A TRUE EYE-OPENER! ❤️</p>
<p>"A friend came to my house for coffee, we sat down and talked about life. After a while I interrupted the conversation and said to him, ′′I\'m going to wash the dishes, I\'ll be right back.”</p>
<p>He looked at me like I told him he was going to build a spaceship. So he said to me with admiration and a little stumped, ′′Glad you help your wife, I rarely help mine because when I do she never thank me. Last week I washed the floor and she didn\'t even tell me to thank you.”</p>
<p>I sat back down with him again and explained to him that I don\'t ′′help′′ my wife. Actually, my wife doesn\'t need help, she needs a partner, a teammate. I\'m her home partner… and due to that, all functions are divided, which is not “help” with household chores.</p>
<p>I don\'t “help” my wife clean the house because I also live in it and I need to clean it too.</p>
<p>I don\'t “help” my wife cook, because I also want to eat and I need to cook too.</p>
<p>I don\'t “help” her washing dishes after eating, because I use these dishes too.</p>
<p>I don\'t “help” my wife with kids, because they are mine too and I have to be a father.</p>
<p>I don\'t “help” my wife wash, extend, fold, and put away laundry because it\'s mine and my kids too.</p>
<p>I don\'t give a “helping hand” at home, I\'m part of it.</p>
<p>Then with respect, I asked my friend when was the last time his wife finished cleaning the house, doing laundry, changing the bedsheets, bathing the kids, cooking, organizing, etc.. and did he say: “thank you?”</p>
<p>I mean a real thank you, like, “Wow, baby!! You\'re amazing!!"</p>
<p>Does this all seem absurd? Does it sound weird to you? When, once in your life, you cleaned the floor, you expected at least an excellence award with great glory... why? Haven\'t you ever thought about that?</p>
<p>Maybe, because for you, macho culture taught you that everything is a woman\'s task.</p>
<p>Maybe you\'ve been taught that all this should be done without you having to move a finger.</p>
<p>So praise her as you would like to be praised, likewise, with the same intensity. Hold her hand and behave like a true companion, and assume your part, don\'t behave like a guest who simply comes to eat, sleep, shower, and satisfy sexual needs... feel at home, in your home.</p>
<p>Change in our society begins in our homes, teaching our children the true sense of fellowship!"</p>

';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>