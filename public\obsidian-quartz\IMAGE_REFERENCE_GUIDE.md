# Dynamic Image Reference System

This system allows you to reference images in your posts using a simple syntax that automatically pulls image data (alt text, captions, commentary) from your `gallery.json` file.

## How to Use

### Basic Syntax

In your markdown files, use this syntax to reference any image from your gallery:

```markdown
{{img:filename}}
```

### Examples

```markdown
# My Post

Here's a great image from my self collection:
{{img:30degrees.png}}

And here's a funny meme:
{{img:22-likes.png}}

You can also reference by the full path:
{{img:self/30degrees.png}}
```

### What Gets Generated

When you use `{{img:filename}}`, the system automatically:

1. **Finds the image** in your gallery.json by filename
2. **Generates proper HTML** with the correct path
3. **Includes alt text** for accessibility
4. **Adds captions** if available
5. **Includes commentary** with a toggle button (if available)
6. **Applies responsive styling**

### Example Output

```html
<div class="dynamic-image-container">
    <img src="img/self/30degrees.png" 
         alt="Angling in a hammock for optimal comfort." 
         loading="lazy" 
         width="1028" 
         height="838">
    <div class="image-caption">Angling in a hammock for optimal comfort.</div>
    <button class="toggle-commentary" onclick="toggleCommentary('commentary-abc123')">Show Commentary</button>
    <div id="commentary-abc123" class="image-commentary" style="display: none;">
        <strong>Commentary:</strong> Many people who try sleeping in a traditional hammock once will say they can't do it because of back or neck pain. In almost all cases, they are laying in the hammock straight, which is incorrect for comfort. Try angling yourself to about 30 degrees, or whatever is optimally comfortable.
    </div>
</div>
```

## How It Works

### Server-Side Processing (Build Time)

When you run `build.php`, the system:

1. Loads your `gallery.json` file
2. Processes each markdown file
3. Finds `{{img:filename}}` references
4. Replaces them with full HTML before converting to PHP

### Client-Side Processing (Runtime)

The JavaScript `image-resolver.js` also runs in the browser to:

1. Handle any dynamic content
2. Process images added after page load
3. Provide fallback for any missed references

## Image Lookup Logic

The system is smart about finding images:

1. **Exact match**: `{{img:30degrees.png}}` → finds `self/30degrees.png`
2. **Basename match**: `{{img:not-smee}}` → finds `self/not-smee.png` 
3. **Extension flexibility**: `{{img:not-smee.jpg}}` → finds `self/not-smee.png`
4. **Partial matching**: Tries to match partial filenames

## Benefits

### For You
- **Single source of truth**: All image metadata in `gallery.json`
- **Easy to use**: Simple `{{img:filename}}` syntax
- **Automatic updates**: Change alt text in JSON, updates everywhere
- **No broken links**: System warns if image not found

### For Readers
- **Better accessibility**: Proper alt text from your curated descriptions
- **Rich context**: Optional commentary provides deeper insights
- **Responsive images**: Proper sizing and loading
- **Consistent styling**: All images follow your design system

## Troubleshooting

### Image Not Found
If you see `[Image not found: filename]`, check:

1. **Filename spelling** in your markdown
2. **Image exists** in gallery.json
3. **File actually exists** in img/ directory

### Missing Alt Text
If images appear without alt text:

1. Check the `alt` field in gallery.json
2. Rebuild the site with `php build.php`

### Commentary Not Showing
If commentary toggle doesn't appear:

1. Ensure `commentary` field exists in gallery.json
2. Check that it's not empty
3. Verify JavaScript is loading properly

## Migration from Old Image Links

### Old Way
```markdown
<img src="../../img/self/not-smee.png" alt="April Chips posing to prove not Mr. Smee from Hook." width="400">
```

### New Way
```markdown
{{img:not-smee.png}}
```

The new way automatically:
- Uses the correct path (no more `../../img/`)
- Pulls alt text from gallery.json
- Includes caption and commentary
- Applies consistent styling
- Works from any directory depth

## Advanced Usage

### Hiding Elements
You can customize the PHP helper to hide certain elements:

```php
// Hide commentary
ImageHelper::createImageHTML('filename.png', 'img/', ['hide_commentary' => true]);

// Hide caption
ImageHelper::createImageHTML('filename.png', 'img/', ['hide_caption' => true]);
```

### Custom Styling
Override CSS classes:
- `.dynamic-image-container` - Main container
- `.image-caption` - Caption text
- `.image-commentary` - Commentary box
- `.toggle-commentary` - Toggle button
- `.missing-image` - Error state

## File Structure

```
public/obsidian-quartz/
├── data/
│   └── gallery.json          # Your image database
├── includes/
│   └── image-helper.php      # Server-side processing
├── js/
│   └── image-resolver.js     # Client-side processing
├── css/
│   └── style.css            # Styling (updated)
└── build.php                # Build script (updated)
```

## Next Steps

1. **Update existing posts**: Replace old `<img>` tags with `{{img:filename}}`
2. **Run build**: Execute `php build.php` to process all files
3. **Test**: Check that images display correctly with alt text and captions
4. **Enjoy**: No more broken image links or missing alt text!
