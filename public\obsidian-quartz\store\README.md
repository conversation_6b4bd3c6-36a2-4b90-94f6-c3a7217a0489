# Store Item Pages

This directory contains individual pages for each store item.

## Files:
- `item-template.php` - Template file for generating new item pages
- `giant-stuffed-axolotl.php` - Giant Stuffed Axolotl (Slightly Singed)
- `vintage-clue-game.php` - Vintage Clue Game (1987)
- `uranium-glass-bowl.php` - Uranium Glass Bowl
- `haunted-typewriter.php` - Possibly Haunted Typewriter (1940s)
- `mystery-plant-cuttings.php` - Mystery Plant Cuttings
- `vintage-camping-percolator.php` - Vintage Camping Coffee Percolator

## Adding New Items:
1. Add the item to `../data/store-items.json`
2. Run `php ../generate-store-pages.php` to create the page
3. Add thumbnail images to `../img/store/`

## Image Naming:
Images should be named according to the filenames in the JSON `images` array.
For example, if an item has `"images": ["axolotl-1.jpg", "axolotl-2.jpg"]`,
place those files in `../img/store/axolotl-1.jpg` and `../img/store/axolotl-2.jpg`.
