<?php
// Auto-generated blog post
// Source: content\ptsd-myth\bugs-addiction.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'homeless advocacy addiction climate masscommunication spiritual CompassionateCities library poems  Why Appreciating Bugs Helps Me Reframe Addiction Bu...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'homeless advocacy addiction climate masscommunication spiritual CompassionateCities library poems  Why Appreciating Bugs Helps Me Reframe Addiction Bu...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\bugs-addiction.md',
);

// Post content
$post_content = '<p>#homeless #advocacy #addiction #climate #masscommunication #spiritual #CompassionateCities #library #poems</p>
<h2>Why Appreciating Bugs Helps Me Reframe Addiction</h2>
Bugs are sometimes framed as gross, invaluable, and non-sentient living things unwanted around us. I want to flip that idea around. Bugs are awesome. They are an invaluable diverse part of our ecosystems that do a bunch of cool things, and make life possible for us. Bugs in their natural element know what they do and how to operate in seeming perfect communication collectively in a colony.
<p>+ Some have a hundred legs, some subsist on filth and rot, and some just like building.
+ Some can be split in half completely, survive, and turn into two separate bugs. 
+ Some bugs will be a little shocked, but mostly not phased losing an entire appendage.
+ Some will eat their mates head after engaging in procreation.
+ Some bugs will eat grosser, less desirable bugs, as a service for allowing rent.
+ Some bugs pollinate our crops, trees, and flowers every year. 
+ Some bugs will eat all your food if you let them. 
+ Some bugs want to live inside of us and our pets as parasites, and we have to be on a type of alert to not let them.
+ Some bugs are essential for the decomposition of life after death
+ Some of those bugs are delicious and nutritious, easy to farm in a small place, and can be a source of food security and delicacy no stranger than the American fixation on Cheese. 
+ Sometimes we act like bugs as people trying to survive, or accomplish a laborious task. 
+ Some bugs will infest buildings, and can often be removed with non-lethal and non-invasive methods
+ Some bugs are beautiful and deserve special protection when their existence is endangered.</p>
<p>Bugs are predictable. Bugs don\'t have the same type of self-awareness we do as humans. They are much more limited in feeling intense emotional states like loss, and shame, and guilt. They do not long for a homeland, or for true love. No fear of a pending mortality (unless they think you are going to squish them, or about to get eaten). Their language with each other is primitive, but functional, sometimes simply through emitt\\ing pheromone smells.</p>
<p>Do you think that bugs experience addiction like we do?</p>
<p>I don\'t think they do.</p>
<p>And I think we could learn something from bugs.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>