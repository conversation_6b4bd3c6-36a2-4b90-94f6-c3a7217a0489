<?php
// Auto-generated blog post
// Source: content\ptsd-myth\client-language.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'April Cyr - 9/27/2023 “There are these two young fish swimming along, and they happen to meet an older fish swimming the other way, who nods at them...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'April Cyr - 9/27/2023 “There are these two young fish swimming along, and they happen to meet an older fish swimming the other way, who nods at them...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\ptsd-myth\\client-language.md',
);

// Post content
$post_content = '
<p>April Cyr - 9/27/2023</p>
<p>“There are these two young fish swimming along, and they happen to meet an older fish swimming the other way, who nods at them and says, “Morning, boys. How’s the water?” And the two young fish swim on for a bit, and then eventually one of them looks over at the other and goes, “What the hell is water?”</p>
<p>This parable was quoted from a graduation speech at Kenyon College, given by David Foster Wallace. I think it encapsulates the prompt for this discussion on the importance of language.</p>
<p>Language is the water we swim in in every interaction, verbal or otherwise. We have a set of shared meanings that we can form ideas and convey information through. And yet, it’s very easy to take language for granted and not give it a second thought.</p>
<p>As someone who is fluent in two languages (English and Spanish) I think about the role of language in working with a diverse populace. As a Student IT Worker, I regularly interact with students and families who speak primarily Spanish, and I am able to speak to them in the language they are most comfortable with. It’s amazing seeing the glow on someone’s face when they are able to speak in the language they are most comfortable with. Code-switching can also occur in many communities, where being able to speak \'properly\' is an asset to survival, and how people speak in safe settings where they can speak as comfortably as at home.</p>
<p>Sometimes the identities someone carries require a level of precision with language to discuss appropriately, such as talking about a disability, sexual or gender orientation, or a life-altering event like the death of a child, or a traumatic event. Getting language wrong in these situations at its mildest can be a turn-off and cause distrust among a client. At its worst, it can create a hostile environment and cause someone to leave and never come back.</p>
<p>The best thing to do is to enter professional interactions with a level of humility, and be willing to learn and take a lead from your clients on how to communicate and understand their needs best. How does your client describe themselves? How is it different from how your client might be described by others who either do not know anything about the client, or who are not culturally competent in the client’s identities?</p>
<p>Something else to consider is literacy. Please do not assume everyone you work with is able to read and write. If your clients need help understanding the terms of a situation, service, or otherwise, be enthusiastic to help explain or read through if necessary. A Human Services Professional may have obtained a High School Diploma, an Associates Degree, a Bachelor’s Degree, or even a Masters. Sometimes clients have not had the privilege of a quality education and becoming literate in the same way you have. Adults with literacy challenges face enough shame as it is, and it\'s important to help them feel comfortable and empowered by providing them with the information they need. Additionally, it\'s important to be patient and understanding when it comes to serving clients.</p>
<p>_Adult literacy in the United States_. (2019, July). National Center for Education Statistics. https://nces.ed.gov/pubs2019/2019179/index.asp</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>