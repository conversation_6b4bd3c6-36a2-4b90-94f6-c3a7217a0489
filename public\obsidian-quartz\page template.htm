<?php
// Start session before any output
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize path constants if not already done
if (!isset($paths) && isset($config)) {
    require_once (isset($base_url) ? $base_url : '') . 'path-helper.php';
    $paths = initPaths($config, __FILE__);

    // Set fallback variables for backward compatibility
    if (!isset($css_path)) $css_path = $paths['css_path'];
    if (!isset($js_path)) $js_path = $paths['js_path'];
    if (!isset($base_url)) $base_url = $paths['base_path'];
}
?>
<!DOCTYPE html>
<html lang="<?php echo $config['site']['language'] ?? 'en'; ?>">

<head>
    <meta charset="<?php echo $config['site']['charset'] ?? 'UTF-8'; ?>">
    <meta name="viewport" content="<?php echo $config['meta']['viewport'] ?? 'width=device-width, initial-scale=1.0'; ?>">

    <!-- Title -->
    <title><?php
        if (isset($page_title)) {
            echo htmlspecialchars($page_title) . ' - ' . htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        } else {
            echo htmlspecialchars($config['site']['title'] ?? 'A. A. Chips\' Blog');
        }
    ?></title>

    <!-- Meta Description -->
    <meta name="description" content="<?php
        echo htmlspecialchars($meta_description ?? $config['site']['description'] ?? '');
    ?>">

    <!-- Meta Keywords -->
    <meta name="keywords" content="<?php
        echo htmlspecialchars($meta_keywords ?? $config['meta']['keywords'] ?? '');
    ?>">

    <!-- Author -->
    <meta name="author" content="<?php echo htmlspecialchars($config['site']['author'] ?? 'A. A. Chips'); ?>">

    <!-- Robots -->
    <meta name="robots" content="<?php echo $config['meta']['robots'] ?? 'index, follow'; ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ($base_url ?? '') . 'favicon.ico'; ?>">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?php echo ($css_path ?? $paths['css_path'] ?? 'css/') . 'style.css?v=' . time(); ?>">
    <link rel="stylesheet" href="<?php echo ($css_path ?? $paths['css_path'] ?? 'css/') . 'comments.css?v=' . time(); ?>">
    <link rel="stylesheet" href="<?php echo ($css_path ?? $paths['css_path'] ?? 'css/') . 'visitor-counter.css?v=' . time(); ?>">

    <?php if (isset($additional_css)) echo $additional_css; ?>

    <!-- External Services -->
    <?php if ($config['social']['clarity_enabled'] ?? false): ?>
    <!-- Microsoft Clarity -->
    <script type="text/javascript">
        (function(c,l,a,r,i,t,y){
            c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
            t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
            y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
        })(window, document, "clarity", "script", "rxj1sxkvdi");
    </script>
    <?php endif; ?>



    <?php if ($config['social']['chatway_enabled'] ?? false): ?>
    <!-- Chatway -->
    <script id="chatway" async="true" src="https://cdn.chatway.app/widget.js?id=QGxjxq7wNQi8"></script>
    <?php endif; ?>
</head>

  <body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <?php include ($paths['includes_path'] ?? 'includes/') . 'header.php'; ?>

      <div class="main-container">
          <main id="main-content" tabindex="-1">
              <div class="content">
                  <?php if (isset($content)) echo $content; ?>
              </div>

              <!-- Comments Section for individual posts -->
              <?php
              // Only show comments on individual post pages (not category indexes)
              if (isset($post_data) && !empty($post_data) && !isset($category_posts) && !isset($content_posts)):
                  // Get post slug from current file
                  $currentFile = $_SERVER['REQUEST_URI'];
                  $postSlug = basename(parse_url($currentFile, PHP_URL_PATH), '.php');
                  if (strpos($postSlug, 'index') === false): // Don't show on index pages

                      // Try multiple paths to find comments-display.php
                      $commentsDisplayPaths = [
                          dirname(__FILE__) . '/comments/comments-display.php',  // From template location
                          ($paths['base_path'] ?? '') . 'comments/comments-display.php',  // Using base path
                          '../comments/comments-display.php',  // Relative from content
                          '../../comments/comments-display.php',  // Relative from deeper content
                          dirname(dirname(__FILE__)) . '/comments/comments-display.php'  // Alternative approach
                      ];

                      $commentsLoaded = false;
                      foreach ($commentsDisplayPaths as $commentsPath) {
                          if (file_exists($commentsPath)) {
                              include $commentsPath;
                              $commentsLoaded = true;
                              break;
                          }
                      }

                      if ($commentsLoaded && function_exists('renderCommentsSection')) {
                          echo renderCommentsSection($postSlug);
                      } else {
                          echo '<div class="comments-error" style="color: #666; font-style: italic; padding: 20px;">Comments temporarily unavailable</div>';
                      }
              ?>
              <?php
                  endif;
              endif;
              ?>

              <!-- Browse Categories Section -->
              <section class="browse-categories">
                  <h2>Browse Categories</h2>
                  <div class="categories-grid">
                      <?php if (isset($related_posts) && !empty($related_posts)): ?>
                          <?php foreach ($related_posts as $post): ?>
                              <a href="<?php echo $post['url']; ?>" class="category-card">
                                  <h3><?php echo htmlspecialchars($post['title']); ?></h3>
                                  <?php if (isset($post['excerpt'])): ?>
                                      <p><?php echo htmlspecialchars($post['excerpt']); ?></p>
                                  <?php endif; ?>
                              </a>
                          <?php endforeach; ?>
                      <?php else: ?>
                          <a href="<?php echo ($base_url ?? '') . 'content/alienation/index.php'; ?>" class="category-card">
                              <h3>Alienation</h3>
                              <p>Family alienation and recovery content.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/climate/index.php'; ?>" class="category-card">
                              <h3>Climate</h3>
                              <p>Environmental and climate action content.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/humor/index.php'; ?>" class="category-card">
                              <h3>Humor</h3>
                              <p>Funny stories and observations.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/inspiration/index.php'; ?>" class="category-card">
                              <h3>Inspiration</h3>
                              <p>Uplifting content and motivational pieces.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/journal/index.php'; ?>" class="category-card">
                              <h3>Journal</h3>
                              <p>Personal reflections and daily thoughts.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/judaism/index.php'; ?>" class="category-card">
                              <h3>Judaism</h3>
                              <p>Jewish identity and spiritual reflections.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/kitchen/index.php'; ?>" class="category-card">
                              <h3>Apple Chip Kitchen</h3>
                              <p>Food, recipes, and kitchen philosophy.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/ptsd-myth/index.php'; ?>" class="category-card">
                              <h3>PTSD Mythology</h3>
                              <p>Mental health advocacy and PTSD awareness.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/street/index.php'; ?>" class="category-card">
                              <h3>Street Advocacy</h3>
                              <p>Homelessness advocacy and social justice.</p>
                          </a>
                          <a href="<?php echo ($base_url ?? '') . 'content/writings/index.php'; ?>" class="category-card">
                              <h3>Writings</h3>
                              <p>Essays, articles, and written works.</p>
                          </a>
                      <?php endif; ?>
                  </div>
              </section>
          </main>

          <aside class="sidebar">
              <?php include ($paths['includes_path'] ?? 'includes/') . 'sidebar.php'; ?>
          </aside>
      </div>
      <?php include ($paths['includes_path'] ?? 'includes/') . 'footer.php'; ?>





      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'script.js'; ?>"></script>
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'comments.js'; ?>"></script>
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'gallery.js'; ?>"></script>
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'color-switcher.js'; ?>"></script>
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'image-resolver.js'; ?>"></script>

      <!-- Donation Modal System - Comment out this line to disable -->
      <script src="<?php echo ($js_path ?? $paths['js_path'] ?? 'js/') . 'donation-modal.js'; ?>"></script>

      <?php if (isset($additional_js)) echo $additional_js; ?>

      <?php if ($config['social']['kofi_enabled'] ?? false): ?>
      <!-- Ko-Fi Support/Donate Button -->
      <script src='https://storage.ko-fi.com/cdn/scripts/overlay-widget.js'></script>
      <script>
        kofiWidgetOverlay.draw('aachips', {
          'type': 'floating-chat',
          'floating-chat.donateButton.text': 'Support me',
          'floating-chat.donateButton.background-color': '#00b9fe',
          'floating-chat.donateButton.text-color': '#fff'
        });
      </script>
      <?php endif; ?>
  </body>
</html>
