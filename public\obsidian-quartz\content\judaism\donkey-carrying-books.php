<?php
// Auto-generated blog post
// Source: content\judaism\donkey-carrying-books.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Donkey Carrying Books -- Ask the Rabbi';
$meta_description = 'The verse suggests that scholars will willingly load themselves with books and learning. A person who has many books but has not digested their contents. Can indicate someone who is well-stocked with knowledge and handles it intelligently.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Donkey Carrying Books -- Ask the Rabbi',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'The verse suggests that scholars will willingly load themselves with books and learning. A person who has many books but has not digested their contents. Can indicate someone who is well-stocked with knowledge and handles it intelligently.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\judaism\\donkey-carrying-books.md',
);

// Post content
$post_content = '<h2><a href="https://oztorah.com/2010/01/donkeys-carrying-books-ask-the-rabbi/ "Permanent Link: Donkeys carrying books – Ask the Rabbi"" class="external-link">Donkeys carrying books – Ask the Rabbi</a></h2>
<p>Q. I hear that the Koran calls Jews “donkeys carrying books”. Should Jews take this as praise or an insult?</p>
<p>A. The saying is actually Jewish and pre-dates the <a href="https://oztorah.com/2007/05/the-koran-and-the-jews-ask-the-rabbi/" class="external-link">Koran</a>.</p>
<p>The idea seems to derive from Genesis 49:14, which says, “<a href="https://oztorah.com/2016/06/rewarding-a-mitzvah-the-etymology-of-issachar/" class="external-link">Issachar</a> is a strong-boned ass”.</p>
<p>Because rabbinic commentary regards Issachar, one of Jacob’s sons, as a scholar, the verse suggests that scholars will willingly load themselves with books and learning (Rashi says, “strong-boned ass: sturdy to bear the yoke of the study of the Torah”).</p>
<p>The Talmud (Avodah Zarah 5b) recommends that, donkey-like, one should make himself able to carry a weight of knowledge. Similarly Rashi (Meg. 28b) says a person should be like a basket capable of holding many books.</p>
<p>The original connotation of the donkey is a compliment and not an insult, though by the time of the Koran it may be that donkeys had fallen in people’s estimation and had become brainless pack-horses: the Koran reference, 62:5, is thus not meant as a praise of Jews but the opposite.</p>
<p>The phrase is found in Jewish philosophy in Bachya ibn Pakuda’s “Chovot HaL’vavot” (“Duties of the Heart”) as an indication of a person who has many books but has not digested their contents. It also appears in this sense in Nachmanides’ works and in the Zohar.</p>
<p>Later rabbinic responsa, however, tend to go back to the earlier concept and use the donkey metaphor in a positive way to indicate someone who is well-stocked with knowledge and handles it intelligently.</p>
<p>This entry is filed under <a href="https://oztorah.com/category/ask-the-rabbi/animals/" class="external-link">Animals</a>, <a href="https://oztorah.com/category/ask-the-rabbi/" class="external-link">Ask The Rabbi</a>, <a href="https://oztorah.com/category/ask-the-rabbi/education-study/" class="external-link">Education & Study</a>, <a href="https://oztorah.com/category/ask-the-rabbi/gentiles-other-faiths/" class="external-link">Gentiles & Other Faiths</a>.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>