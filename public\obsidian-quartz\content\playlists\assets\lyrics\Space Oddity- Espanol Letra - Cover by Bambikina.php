<?php
// Auto-generated blog post
// Source: content\playlists\assets\lyrics\Space Oddity- Espanol Letra - Cover by Bambikina.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = '4 SPACE ODDITY versión castellano español BAMBIKINA cover - YouTubehttps://www.youtube.com/watch?v=GDojbym0xEk Control on rastro a auto Tom Control ...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => '4 SPACE ODDITY versión castellano español BAMBIKINA cover - YouTubehttps://www.youtube.com/watch?v=GDojbym0xEk Control on rastro a auto Tom Control ...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\lyrics\\Space Oddity- Espanol Letra - Cover by Bambikina.md',
);

// Post content
$post_content = '<p><a href="https://www.youtube.com/watch?v=GDojbym0xEk" class="external-link">(4) SPACE ODDITY versión castellano (español) BAMBIKINA cover - YouTube</a></p>
<p>Control on rastro a auto Tom
Control on rastro a auto Tom
Todo listo para empeza la conexion
control en rastro a auto tom. 
la cuentatras ya comenzo
ensendiamo tu suerte la mision,</p>
<p>aki vas ya rastro en auto tom
a conseki do ya lugar,
its a little bit de la prencateal[?]
salga fuera de la capsule espacial,
aki tom tomando a este sesion
estoy saliendo a ya,
flotando en mediaday la immencidad,
a estrellas y a mis sonidad
ya aki esta sentada en  esta nabe
ooo a mis pies
aki res azul es ta tanta luz</p>
<p>ya mi reste millas de miojar
(serspia tante pas?)
quiero a mi mijer
creo que jamas podre volver
valsalas a auto tom ha\\y problemas en el control, 
logitech connexion</p>
<p>perdemos la conexion
perdemos la conexion
estoy aki floating en un universo
la luna mis pies
a tira es asul
espere ta tanta luz</p>


<p><div class="text_to_html">That story is wild. I am not sure I have the emotional maturity to handle a conversation about that one in class. The controller for the ship was a Logitech Playstation controller. Experts had warned the company for years about fractures to carbon fiber frame submarines. That one was a completely preventable accident caused by not listening to experts and engaging in an extremely dangerous activity no one should engage in without intimately understanding the broad range of Sciences involved in deep sea expeditions. The implosion was instant, no one suffered or knew that it happened till it was over. I believe culturally, that there are times when it is appropriate to laugh about death. I\'ve listened to Space Oddity by <a href="https://www.youtube.com/watch?v=GDojbym0xEk">David Bowie, in particular a Spanish cover on Youtube</a>. "Floating in a tin can", and in the Spanish one, "Perdemos la connexion(We\'re losing the connection..)" playing in my mind as "Logitech connection.."</div></p>
<p><a href="https://www.youtube.com/watch?v=swONyfGDWoU" class="external-link">Leiva - Costa de Oaxaca - YouTube</a></p>
<p>Michoacan & Michigan
+ Both derived from Indigenous Native languages, 
		+ Michoacan: from Nahuatl language " Place of fish" and Purepecha language "place by the water."
		+ Michigan from Ojibwe, "Great or large lake"
+ The term Tarasco/Tarasca is more derogatory, but comes from a Purepecha word meaning \'Father-in-law,\' from arranging marriages with the Spanish. Not how Purepecha identify themselves. Like calling them \'Father-in-laws.\'
LOL the "limon es para cordor la grasa"</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>