<?php
// Auto-generated blog post
// Source: content\playlists\assets\mercy\Leaning on the Everlasting Arms En Los Brazos de mi Salvador.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'Leaning on the Everlasting Arms / En Los Brazos de mi Salvador !https://yt3.ggpht.com/ytc/AIdrolcgldWaLbADRkjKQg3-yHLY4JaKnM8IEER47taMuj3Qg=s48-c-k-c0...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'Leaning on the Everlasting Arms / En Los Brazos de mi Salvador !https://yt3.ggpht.com/ytc/AIdrolcgldWaLbADRkjKQg3-yHLY4JaKnM8IEER47taMuj3Qg=s48-c-k-c0...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\playlists\\assets\\mercy\\Leaning on the Everlasting Arms En Los Brazos de mi Salvador.md',
);

// Post content
$post_content = '<h1>Leaning on the Everlasting Arms / En Los Brazos de mi Salvador</h1>
<p><a href="https://yt3.ggpht.com/ytc/AIdro_lcgldWaLbADRkjKQg3-yHLY4JaKnM8IEER47taMuj3Qg=s48-c-k-c0x00ffffff-no-rj" class="external-link">![</a>](https://www.youtube.com/@alasdeaguilabilingualworsh2619)</p>
<p><a href="https://www.youtube.com/@alasdeaguilabilingualworsh2619" class="external-link">Alas de Aguila Bilingual Worship</a></p>
<p>145 subscribers</p>
<p><__slot-el></p>
<p>Subscribe</p>
<p><__slot-el></p>
<p>19</p>
<p>Share</p>
<p>Download</p>
<p>Clip</p>
<p>Save</p>
<p>681 views Jul 13, 2020 <a href="https://www.youtube.com/hashtag/11243381" class="external-link">#11243381</a></p>
<p>Listen to Alas de Aguila worship, singing a bilingual (English/Spanish) version of Leaning on the Everlasting Arms. Escuche la adoración de Alas de Aguila, cantando una versión bilingüe (inglés / español) de En Los Brazos de mi Salvador Everlasting Arms/Tis so Sweet to Trust in Jesus What a fel-low-ship, what a joy di-vine, Lean-ing on the ev-er-last-ing arms, What a bless-ed-ness, what a peace is mine, Lean-ing on the ev-er-last-ing arms. Lean-ing, lean-ing, Safe and se-cure from all a-larms, Lean-ing, lean-ing, lean-ing on the ev-er-last-ing arms. Cuán dulce es vivir, cuán dulce es gozar 
En los brazos de mi Salvador!</p>
<p>Allí quiero ir y con él morar, 
Siendo objeto de su tierno amor.</p>
<p>Libre, salvo del pecado y del temor, 
Libre, salvo, en los brazos de mi Salvador.</p>
<p>What have I to dread, what have I to fear, Lean-ing on the ev-er-last-ing arms? I have bless-ed peace with my Lord so near, Lean-ing on the ev-er-last-ing arms. Lean-ing, lean-ing, Safe and se-cure from all a-larms, Lean-ing, lean-ing, lean-ing on the ev-er-last-ing arms.</p>
<p>Oh, cuán dulce es fiar en Cristo, Y entregarle todo a Él; Esperar en Sus promesas, Y en Sus sendas serle fiel! Cristo, Cristo cuánto te amo tu poder probaste en mí; Cristo, Cristo puro y santo Siempre quiero fiar en Ti. O how sweet to trust in Jesus, Just to trust His cleansing blood; Just in simple faith to plunge me, ’Neath the healing, cleansing flood. Jesus, Jesus, how I trust him! How I\'ve proved him o\'er and o\'er! Jesus, Jesus, precious Jesus! O for grace to trust him more! Cristo, Cristo cuánto te amo tu poder probaste en mí; Cristo, Cristo puro y santo Siempre quiero fiar en Ti. “Leaning on the Everlasting Arms” Words and Music by: Anthony Johnson Showalter & Elisha Albright Hoffman ©Public Domain Translation by: Pedro Grado “Tis So Sweet To Trust in Jesus” Words and Music by: Louisa M. R. Stead & William James Kirkpatrick ©Public Domain Translation by: Vicente Mendoza CCLI <a href="https://www.youtube.com/hashtag/11243381" class="external-link">#11243381</a></p>
<p><a href="https://yt3.ggpht.com/ytc/AIdro_lcgldWaLbADRkjKQg3-yHLY4JaKnM8IEER47taMuj3Qg=s88-c-k-c0x00ffffff-no-rj" class="external-link">![</a></p>
<h3>Alas de Aguila Bilingual Worship</h3>
<p>145 subscribers</p>

<p>](https://www.youtube.com/@alasdeaguilabilingualworsh2619)</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>