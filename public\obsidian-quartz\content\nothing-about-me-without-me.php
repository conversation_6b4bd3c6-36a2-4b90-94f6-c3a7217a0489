<?php
// Auto-generated blog post
// Source: content\nothing-about-me-without-me.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Nothing About Me Without Me - Center for Disability Rights';
$meta_description = 'As disabled people, we so often have to walk fine lines to be successful in the able bodied world. Whether it’s in the work place, in school or just in the general public there always seems to be a fine line between getting our needs met and maintaining our independence.';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Nothing About Me Without Me - Center for Disability Rights',
  'author' => 'Sydney Chasteen - Center for Disability Rights',
  'date' => '2025-08-05',
  'excerpt' => 'As disabled people, we so often have to walk fine lines to be successful in the able bodied world. Whether it’s in the work place, in school or just in the general public there always seems to be a fine line between getting our needs met and maintaining our independence.',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\nothing-about-me-without-me.md',
);

// Post content
$post_content = '<h2>Nothing About Me Without Me?</h2>
<p>As disabled people, we so often have to walk fine lines to be successful in the able bodied world. Whether it’s in the work place, in school or just in the general public there always seems to be a fine line between getting our needs met and maintaining our independence. Many times this also means dealing with a litany of micro aggressions that have been ingrained in society.</p>
<p>Several of these, leaning on a person’s wheelchair for example, or overt use of words like crazy to describe people or situations, can be talked about and explained to the other person. But others are not so easily brought up in conversation because it is harder to explain why the behavior is seen by the disabled person as a micro aggression in the first place.</p>
<p>One of the most common that I deal with on a regular basis, usually from family or someone who is otherwise legitimately being helpful, is miss use of the words “we” or “our” when talking about something that only concerns me. Generally this happens when talking about a doctors appointment or something that I cannot accomplish on my own (i.e. I need transportation etc). Someone will say we’re going to our appointment or, ask a doctor for help with our problem.</p>
<p>This does several things. First it creates a situation in which the person is equally involved in an issue that at its core only involves me. And second it invites further help that I had not asked for yet and may have no intention of asking for. Both of these undermine my autonomy in the situation.</p>
<p>Granted these instances may only be occasional, the habit of one or two people at most. But for that relationship the behavior is detrimental. It allows for the continued infantilization of the disabled individual, whether they’re a child or an adult which could carry lasting effects on that individual’s growth and development of other relationships.</p>
<p>But again bringing this issue to light, let alone getting the other person to understand, I can say first hand is difficult. However it is necessary. Clearly explaining the need for the separation between an action by another person that you are grateful for and your personal involvement in a situation concerning you solidifies the feeling of independence for a disabled person, something that is so vital for many in the disability community.</p>
<p>The first step has to be starting that conversation. Set the boundary between showing gratitude for the help you receive and allowing the person helping you too much liberty in the situation that only really involves you. If approached in the right way, you will create a more transparent relationship with a better informed ally.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>