<?php
// Auto-generated blog post
// Source: content\dreamers-blessing.md

// Load path helper and configuration with fallback
$pathPrefix = '';
if (file_exists(__DIR__ . '/' . $pathPrefix . 'path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . 'path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . 'config.php';
} elseif (file_exists(__DIR__ . '/' . $pathPrefix . '../path-helper.php')) {
    require_once __DIR__ . '/' . $pathPrefix . '../path-helper.php';
    $config = include __DIR__ . '/' . $pathPrefix . '../config.php';
} else {
    die('Could not find path-helper.php');
}
$paths = initPaths($config, __FILE__);

// Page variables
$page_title = 'Untitled';
$meta_description = 'A Blessing for Dreamers May you remember your dreams when you wake—   not as fragile things that slip through your fingers, but as vivid stories etc...';
$meta_keywords = 'A. A. Chips, blog';
$css_path = $paths['css_path'];
$js_path = $paths['js_path'];
$base_url = $paths['base_path'];
$thumbnail = null;
$related_posts = [];
$random_posts = [];
// This will be populated by the sidebar include

// Post metadata
$post_data = array (
  'title' => 'Untitled',
  'author' => 'A. A. Chips',
  'date' => '2025-08-05',
  'excerpt' => 'A Blessing for Dreamers May you remember your dreams when you wake—   not as fragile things that slip through your fingers, but as vivid stories etc...',
  'tags' => 
  array (
  ),
  'source_file' => 'content\\dreamers-blessing.md',
);

// Post content
$post_content = '<h3><strong>A Blessing for Dreamers</strong></h3>
<p><strong>May you remember your dreams when you wake—</strong>  
not as fragile things that slip through your fingers, but as vivid stories etched into your bones.</p>
<p><strong>May you become lucid in them,</strong> suddenly aware that the world around you bends to your will.  
May you laugh with the joy of discovery, realizing you can fly over cities, dive into oceans, or walk through walls.  
May you taste food so rich, so perfectly spiced, that you wake up craving what never existed—yet somehow was real.</p>
<p><strong>May you visit places you’ve never been</strong> and find yourself fluent in languages you don’t know.  
May time stretch like taffy—a single dream-night holding weeks of adventure, love, or quiet peace.</p>
<p><strong>May you meet someone in the haze of sleep</strong> who leaves you breathless with their strangeness, their beauty, their impossible familiarity.  
May they linger in your mind long after dawn, so that you scribble their name on a napkin, hum their melody in the shower, or search for their face in every stranger you pass.</p>
<p><strong>May your late loved ones visit you there,</strong> whole and laughing, reminding you that love outlasts time.  
May your old pets curl up beside you in dream-grass, warm and real for a little while longer.</p>
<p><strong>And when you wake—</strong>  
may you carry the dream’s magic into your day, so that the gas station clerk, the barista, the person who cuts you off in traffic—all of them glow with that same elusive wonder.</p>
<p><strong>May your car start without trouble.</strong>  
May your coffee be strong.  
And may you return to that other world tonight, wherever—and _whoever_—you choose to be.</p>
<h3><strong>Why This Blessing?</strong></h3>
<p>Lucid dreaming is more than a neat trick; it’s a reminder that our minds are vast, playful, and capable of boundless creation. Some of my most profound joys (and weirdest adventures) have happened in dreams—swimming through bioluminescent caves, reuniting with those I’ve lost, even once eating a taco so delicious I _still_ think about it. (Dream-tacos are undefeated.)</p>
<p>This blessing is my wish for you: that your hidden world behind closed eyes is as vibrant, free, and nourishing as you need it to be.</p>
<p><strong>Sweet dreams, dreamers.</strong></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta">
            <?php echo implode(' | ', $metadata); ?>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include $paths['template_path'];
?>